import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import DashboardLayout from './DashboardLayout';
import { dashboardAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import { showToast } from '../utils/toast';

const Branches = () => {
  // Get user data from authentication context
  const { user } = useAuth();

  // State management
  const [branches, setBranches] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filterCity, setFilterCity] = useState('');

  // Fetch branches data from API
  useEffect(() => {
    const fetchBranches = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await dashboardAPI.getBranches();

        if (response.success && response.data && response.data.branches) {
          setBranches(response.data.branches);
        } else {
          throw new Error('Invalid response format');
        }
      } catch (err) {
        console.error('Error fetching branches:', err);
        setError(err.message || 'Failed to load branches');
        showToast.error('Failed to load branches. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchBranches();
  }, []);

  // Get unique cities for filter (based on API data)
  const cities = [...new Set(branches.map(branch => branch.city))];

  // Filter branches based on city
  const filteredBranches = filterCity
    ? branches.filter(branch => branch.city === filterCity)
    : branches;

  const getComplianceColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getComplianceBgColor = (score) => {
    if (score >= 90) return 'bg-green-100';
    if (score >= 50) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  // Calculate compliance statistics based on API data
  const excellentCount = filteredBranches.filter(b => b.complianceScore >= 90).length;
  const goodCount = filteredBranches.filter(b => b.complianceScore >= 50 && b.complianceScore < 90).length;
  const needsAttentionCount = filteredBranches.filter(b => b.complianceScore < 50).length;

  // Loading state
  if (loading) {
    return (
      <DashboardLayout user={user}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600 font-body">Loading branches...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <DashboardLayout user={user}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to load branches</h3>
            <p className="text-gray-600 font-body mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout user={user}>
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center flex-wrap gap-5 justify-between">
          <div className="flex-1">
            <h1 className="text-3xl font-heading font-bold text-gray-900 mb-2">
              Branch Management
            </h1>
            <p className="text-gray-600 font-body">
              Monitor and manage all branch locations and their compliance status.
            </p>
          </div>
        </div>
      </div>

      {/* Filter and Stats Bar */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
          {/* Left side - Filters */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            <h3 className="text-lg font-semibold text-gray-900 font-heading">Branch Overview</h3>

            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 bg-gray-50 rounded-lg px-3 py-2">
                <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <select
                  value={filterCity}
                  onChange={(e) => setFilterCity(e.target.value)}
                  className="bg-transparent border-none text-sm font-medium text-gray-700 font-body focus:ring-0 focus:outline-none cursor-pointer"
                >
                  <option value="">All Cities</option>
                  {cities.map(city => (
                    <option key={city} value={city}>{city}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Right side - Stats */}
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600 font-heading">{excellentCount}</p>
              <p className="text-xs text-gray-600 font-body">Excellent (≥90%)</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600 font-heading">{goodCount}</p>
              <p className="text-xs text-gray-600 font-body">Good (50-89%)</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600 font-heading">{needsAttentionCount}</p>
              <p className="text-xs text-gray-600 font-body">Needs Attention (&lt;50%)</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 font-heading">{filteredBranches.length}</p>
              <p className="text-xs text-gray-600 font-body">Total Branches</p>
            </div>
          </div>
        </div>
      </div>

      {/* All Branches Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-heading font-bold text-gray-900">All Branches</h2>
          <div className="flex items-center space-x-2 text-sm text-gray-600 font-body">
            <span>Showing {filteredBranches.length} of {branches.length} branches</span>
          </div>
        </div>

        {/* Branches Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredBranches?.length === 0 ? (
            <div className="col-span-4 text-center text-gray-600 font-body">No branches found</div>
          ) : filteredBranches.map((branch) => (
            <div key={branch.id} className="group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:border-primary-200 transition-all duration-300 cursor-pointer">
              {/* Branch Image */}
              <div className="relative overflow-hidden">
                <img
                  src={branch.image}
                  alt={branch.name}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                {/* Overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* Compliance badge */}
                <div className="absolute top-3 right-3">
                  <div className={`px-3 py-1 rounded-full text-xs font-semibold shadow-lg backdrop-blur-sm ${branch.complianceScore >= 90
                    ? 'bg-green-100/90 text-green-800 border border-green-200'
                    : branch.complianceScore >= 50
                      ? 'bg-yellow-100/90 text-yellow-800 border border-yellow-200'
                      : 'bg-red-100/90 text-red-800 border border-red-200'
                    }`}>
                    {branch.complianceScore}%
                  </div>
                </div>

                {/* Quick action overlay */}
                <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button className="text-white text-sm font-medium hover:text-primary-200 transition-colors duration-200">
                    View Branch Details →
                  </button>
                </div>
              </div>

              {/* Branch Info */}
              <div className="p-5">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-heading font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                      {branch.name}
                    </h3>
                    <div className="flex items-center space-x-2 mb-2">
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <p className="text-sm text-gray-600 font-body">
                        {branch.city}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
                      </svg>
                      <p className="text-xs text-gray-500 font-body">
                        {branch.stats?.totalSnapshots || 0} snapshots
                      </p>
                    </div>
                  </div>
                  <button className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 p-1 hover:bg-gray-100 rounded">
                    <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                    </svg>
                  </button>
                </div>

                {/* Compliance Score */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-semibold text-gray-700 font-body">Compliance Score</span>
                    <span className={`text-sm font-bold ${getComplianceColor(branch.complianceScore)}`}>
                      {branch.complianceScore}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className={`h-2.5 rounded-full transition-all duration-500 ${branch.complianceScore >= 90
                        ? 'bg-gradient-to-r from-green-500 to-green-600'
                        : branch.complianceScore >= 50
                          ? 'bg-gradient-to-r from-yellow-500 to-yellow-600'
                          : 'bg-gradient-to-r from-red-500 to-red-600'
                        }`}
                      style={{ width: `${branch.complianceScore}%` }}
                    ></div>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex items-center space-x-2">
                  <Link
                    to={`/snapshots/${branch.id}`}
                    className="flex-1 inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-700 bg-primary-50 hover:bg-primary-100 rounded-lg transition-colors duration-200 font-body"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    View Snapshots
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Branches;
