import { useState } from 'react';
import { Link } from 'react-router-dom';

const ForgotPassword = () => {
  const [formData, setFormData] = useState({
    email: ''
  });

  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Forgot password request:', formData);
    setIsSubmitted(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-teal-50 flex items-center justify-center p-4 md:p-10">
      <div className="w-full max-w-6xl bg-white rounded-3xl shadow-2xl overflow-hidden backdrop-blur-sm border border-white/20">
        <div className="flex flex-col lg:flex-row min-h-[600px]">
          {/* Left Side - Branding */}
          <div className="lg:w-1/2 bg-gradient-to-br from-blue-600 via-cyan-600 to-teal-600 p-12 flex flex-col items-center justify-center relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-20">
              <div className="w-full h-full bg-gradient-to-br from-white/30 via-transparent to-white/10"></div>
              {/* Decorative elements */}
              <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute bottom-20 right-10 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute top-1/2 right-1/4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
            </div>

            {/* Logo */}
            <div className="relative z-10 text-center">
              <div className="mb-8">
                {/* Vista Guard Logo */}
                <div className="flex items-center justify-center mb-8">
                  <div className="relative">
                    {/* Logo Icon with enhanced styling */}
                    <div className="flex items-center p-4 bg-white/20 rounded-2xl backdrop-blur-sm border border-white/30">
                      <img className='h-16 w-auto filter brightness-0 invert' src="./logo.png" alt="logo" />
                    </div>
                  </div>
                </div>

                {/* Brand Name */}
                <h1 className="text-4xl lg:text-5xl font-heading font-bold text-white mb-6 drop-shadow-lg">
                  VistaGuard
                </h1>

                {/* Tagline */}
                <p className="text-lg text-white/90 font-medium leading-relaxed max-w-sm mx-auto mb-8">
                  Don't worry, we'll help you get back to securing your vision.
                </p>

                {/* Security features */}
                <div className="space-y-4">
                  <div className="flex items-center text-white/80 text-sm">
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span>Secure password reset</span>
                  </div>
                  <div className="flex items-center text-white/80 text-sm">
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                      </svg>
                    </div>
                    <span>Email verification</span>
                  </div>
                  <div className="flex items-center text-white/80 text-sm">
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span>Quick recovery</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Forgot Password Form */}
          <div className="lg:w-1/2 p-8 lg:p-12 flex flex-col justify-center bg-gradient-to-br from-white to-gray-50">
            <div className="w-full max-w-md mx-auto">
              {/* Header */}
              <div className="text-center mb-8">
                {/* Small Logo for mobile */}
                <div className="mb-6 lg:hidden">
                  <div className="flex items-center justify-center">
                    <div className="p-3 bg-gradient-to-r from-blue-100 to-cyan-100 rounded-xl">
                      <img className='h-12 w-auto' src="./logo.png" alt="logo" />
                    </div>
                  </div>
                </div>

                {!isSubmitted ? (
                  <>
                    <div className="mb-6">
                      <h2 className="text-3xl lg:text-4xl font-heading font-bold text-gray-900 mb-3">
                        Forgot Password?
                      </h2>
                      <p className="text-gray-600 font-body text-lg leading-relaxed">
                        No worries! Enter your email and we'll send you reset instructions.
                      </p>
                    </div>

                    {/* Progress indicator */}
                    <div className="flex items-center justify-center space-x-2 mb-6">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      <div className="w-8 h-1 bg-blue-200 rounded-full"></div>
                      <div className="w-2 h-2 bg-blue-200 rounded-full"></div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="mb-6">
                      <div className="w-20 h-20 bg-gradient-to-r from-green-100 to-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                        <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </div>
                    </div>
                    <div className="mb-6">
                      <h2 className="text-3xl lg:text-4xl font-heading font-bold text-gray-900 mb-3">
                        Check Your Email
                      </h2>
                      <p className="text-gray-600 font-body text-lg leading-relaxed">
                        We've sent password reset instructions to your email address.
                      </p>
                    </div>

                    {/* Success progress indicator */}
                    <div className="flex items-center justify-center space-x-2 mb-6">
                      <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                      <div className="w-8 h-1 bg-green-600 rounded-full"></div>
                      <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                    </div>
                  </>
                )}
              </div>

              {!isSubmitted ? (
                /* Forgot Password Form */
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Email Field */}
                  <div className="space-y-2">
                    <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                        </svg>
                      </div>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="Enter your email address"
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-body placeholder-gray-400 outline-none bg-white shadow-sm hover:shadow-md"
                        required
                      />
                    </div>
                  </div>

                  {/* Reset Password Button */}
                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-body shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    <span className="flex items-center justify-center">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      Send Reset Instructions
                    </span>
                  </button>

                  {/* Divider */}
                  <div className="relative my-6">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-300"></div>
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-2 bg-white text-gray-500">Remember your password?</span>
                    </div>
                  </div>

                  {/* Back to Login Link */}
                  <div className="text-center">
                    <Link
                      to="/login"
                      className="inline-flex items-center justify-center w-full px-4 py-3 border border-blue-300 rounded-xl text-blue-700 bg-blue-50 hover:bg-blue-100 font-semibold transition-all duration-200 hover:shadow-md"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                      </svg>
                      Back to Login
                    </Link>
                  </div>
                </form>
              ) : (
                /* Success State */
                <div className="space-y-6">
                  {/* Email Confirmation */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6 text-center">
                    <p className="text-sm text-gray-600 font-body mb-2">
                      Reset instructions sent to:
                    </p>
                    <p className="text-base font-semibold text-gray-900 font-body">
                      {formData.email}
                    </p>
                  </div>

                  {/* Instructions */}
                  <div className="text-center space-y-4">
                    <p className="text-sm text-gray-600 font-body leading-relaxed">
                      Didn't receive the email? Check your spam folder or try sending again.
                    </p>
                    <button
                      onClick={() => setIsSubmitted(false)}
                      className="inline-flex items-center px-4 py-2 text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200 font-body hover:bg-blue-50 rounded-lg"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      Resend Email
                    </button>
                  </div>

                  {/* Divider */}
                  <div className="relative my-6">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-300"></div>
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-2 bg-white text-gray-500">Ready to sign in?</span>
                    </div>
                  </div>

                  {/* Back to Login */}
                  <div className="text-center">
                    <Link
                      to="/login"
                      className="inline-flex items-center justify-center w-full px-4 py-3 border border-blue-300 rounded-xl text-blue-700 bg-blue-50 hover:bg-blue-100 font-semibold transition-all duration-200 hover:shadow-md"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                      </svg>
                      Back to Login
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
