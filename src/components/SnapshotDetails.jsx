import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import DashboardLayout from './DashboardLayout';
import { useAuth } from '../context/AuthContext';
import { dashboardAPI } from '../services/api';
import { showToast } from '../utils/toast';

const SnapshotDetails = () => {
  // Get user data from authentication context and snapshot ID from URL
  const { user } = useAuth();
  const { id: snapshotId } = useParams();

  // State management
  const [snapshotData, setSnapshotData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch snapshot details from API
  useEffect(() => {
    const fetchSnapshotDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await dashboardAPI.getSnapshotDetails(snapshotId);

        if (response.success && response.data && response.data.snapshot) {
          setSnapshotData(response.data.snapshot);
        } else {
          throw new Error('Invalid response format');
        }
      } catch (err) {
        console.error('Error fetching snapshot details:', err);
        setError(err.message || 'Failed to load snapshot details');
        showToast.error('Failed to load snapshot details. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (snapshotId) {
      fetchSnapshotDetails();
    }
  }, [snapshotId]);

  // Calculate compliance stats from API data
  const reports = snapshotData?.reports || [];
  const checkedReports = reports.filter(report => report.status === 'CHECKED');
  const uncheckedReports = reports.filter(report => report.status === 'UNCHECKED');

  // Loading state
  if (loading) {
    return (
      <DashboardLayout user={user}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600 font-body">Loading snapshot details...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <DashboardLayout user={user}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to load snapshot</h3>
            <p className="text-gray-600 font-body mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <DashboardLayout user={user}>
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center flex-wrap gap-5 justify-between">
          <div className="flex-1">
            <h1 className="text-3xl font-heading font-bold text-gray-900 mb-2">
              Snapshot
            </h1>
            <p className="text-gray-600 font-body">
              Review the specific non-compliance detected for Branch 003.
            </p>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="mb-10 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="group bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-lg hover:border-blue-200 transition-all duration-300 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-blue-50 rounded-full transform translate-x-6 -translate-y-6 opacity-50"></div>
          <div className="flex items-center relative">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-100 to-blue-200 rounded-xl flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow duration-300">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-600 font-body mb-1">Total Reports</p>
              <p className="text-3xl font-bold text-gray-900 font-heading group-hover:scale-105 transition-transform duration-300">{snapshotData?.stats?.totalReports || 0}</p>
            </div>
          </div>
        </div>

        <div className="group bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-lg hover:border-green-200 transition-all duration-300 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-green-50 rounded-full transform translate-x-6 -translate-y-6 opacity-50"></div>
          <div className="flex items-center relative">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-gradient-to-r from-green-100 to-green-200 rounded-xl flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow duration-300">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-600 font-body mb-1">Checked Reports</p>
              <div className="flex items-baseline space-x-2">
                <p className="text-3xl font-bold text-green-600 font-heading group-hover:scale-105 transition-transform duration-300">{snapshotData?.stats?.checkedReports || 0}</p>
                <span className="text-sm font-medium text-green-600">
                  {snapshotData?.stats?.totalReports ? Math.round((snapshotData.stats.checkedReports / snapshotData.stats.totalReports) * 100) : 0}%
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="group bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-lg hover:border-red-200 transition-all duration-300 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-red-50 rounded-full transform translate-x-6 -translate-y-6 opacity-50"></div>
          <div className="flex items-center relative">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-gradient-to-r from-red-100 to-red-200 rounded-xl flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow duration-300">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-semibold text-gray-600 font-body mb-1">Unchecked Reports</p>
              <div className="flex items-baseline space-x-2">
                <p className="text-3xl font-bold text-red-600 font-heading group-hover:scale-105 transition-transform duration-300">{snapshotData?.stats?.uncheckedReports || 0}</p>
                <span className="text-sm font-medium text-red-600">
                  {snapshotData?.stats?.totalReports ? Math.round((snapshotData.stats.uncheckedReports / snapshotData.stats.totalReports) * 100) : 0}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Snapshot */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-heading font-bold text-gray-900">Snapshot Analysis</h2>
          </div>

          {/* Snapshot Image */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden group">
            <div className="relative">
              <img
                src={snapshotData?.image || 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=600&h=400&fit=crop'}
                alt="Workplace snapshot"
                className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              {/* Overlay with zoom button */}
              <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-20 transition-all duration-300 flex items-center justify-center">
                <button className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transform hover:scale-110">
                  <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 font-body mb-1">
                    <span className="font-semibold">Branch:</span> {snapshotData?.branch?.name || 'Unknown Branch'} - {snapshotData?.branch?.city || 'Unknown City'}
                  </p>
                  <p className="text-sm text-gray-600 font-body">
                    <span className="font-semibold">Captured:</span> {formatTimestamp(snapshotData?.createdAt)}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${snapshotData?.type === 'COMPLAINT'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-green-100 text-green-800'
                    }`}>
                    {snapshotData?.type === 'COMPLAINT' ? 'Non-Compliant' : 'Compliant'}
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {snapshotData?.stats?.totalReports || 0} Reports
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Compliance Report */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-heading font-bold text-gray-900">Compliance Report</h2>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {/* Header */}
            <div className="p-6 border-b border-gray-200">
              <p className="text-gray-600 font-body mb-4">
                Detailed audit of compliance items.
              </p>

              {/* Download Report Button */}
              {
                snapshotData?.reportPDF ? (
                  <a href={snapshotData.reportPDF} target="_blank" rel="noopener noreferrer" className="w-full inline-flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200 font-body">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    View Full Report PDF
                  </a>
                ) : null
              }
            </div>

            {/* Compliance Items */}
            <div className="p-6">
              {/* Progress bar */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Report Completion</span>
                  <span className="text-sm font-bold text-gray-900">
                    {snapshotData?.complianceRate || 0}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${snapshotData?.complianceRate || 0}%` }}
                  ></div>
                </div>
              </div>

              <div className="space-y-3">
                {reports.length > 0 ? reports.map((report, index) => (
                  <div key={report.id} className={`group flex items-start space-x-4 p-4 rounded-xl transition-all duration-200 ${report.status === 'CHECKED'
                    ? 'bg-green-50 hover:bg-green-100 border border-green-200'
                    : 'bg-yellow-50 hover:bg-yellow-100 border border-yellow-200'
                    }`}>
                    {/* Status Icon */}
                    <div className="flex-shrink-0 mt-0.5">
                      {report.status === 'CHECKED' ? (
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      ) : (
                        <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center shadow-sm">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>

                    {/* Report Text */}
                    <div className="flex-1">
                      <p className={`text-sm font-medium font-body ${report.status === 'CHECKED' ? 'text-green-900' : 'text-yellow-900'
                        }`}>
                        {report.details || 'No details provided'}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Report #{index + 1} • {report.status === 'CHECKED' ? 'Reviewed' : 'Pending Review'} • {formatTimestamp(report.createdAt)}
                      </p>
                    </div>

                    {/* Action button */}
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <button className="p-1 hover:bg-white rounded-md transition-colors duration-200">
                        <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                )) : (
                  <div className="text-center py-8">
                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <p className="text-gray-500 font-body">No reports available for this snapshot</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>


    </DashboardLayout>
  );
};

export default SnapshotDetails;
