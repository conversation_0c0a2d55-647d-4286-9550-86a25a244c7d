import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { authToasts } from '../utils/toast';

const Login = () => {
  const navigate = useNavigate();
  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldErrors, setFieldErrors] = useState({});

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/branches', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // Clear errors when component mounts or form data changes
  useEffect(() => {
    if (error) {
      clearError();
    }
    // Clear field errors when form data changes
    if (Object.keys(fieldErrors).length > 0) {
      setFieldErrors({});
    }
  }, [formData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFieldErrors({}); // Clear previous field errors

    try {
      const result = await login(formData);

      if (result.success) {
        authToasts.loginSuccess(result.user.name);
        navigate('/branches', { replace: true });
      } else {
        if (result?.details) {
          // Create field errors object, handling multiple errors per field
          const errors = {};
          result.details.forEach(detail => {
            if (errors[detail.field]) {
              // If field already has an error, combine them
              errors[detail.field] += ` ${detail.message}`;
            } else {
              errors[detail.field] = detail.message;
            }
          });
          setFieldErrors(errors);

          // Also show toast with all validation errors
          authToasts.validationError(result.error, result.details);
        } else {
          authToasts.loginError(result.error);
        }
      }
    } catch (error) {
      // Check if it's a validation error
      if (error?.details) {
        // Create field errors object, handling multiple errors per field
        const errors = {};
        error.details.forEach(detail => {
          if (errors[detail.field]) {
            // If field already has an error, combine them
            errors[detail.field] += ` ${detail.message}`;
          } else {
            errors[detail.field] = detail.message;
          }
        });
        setFieldErrors(errors);

        // Also show toast with all validation errors
        authToasts.validationError(error.error, error.details);
      } else {
        authToasts.loginError(error.error);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4 md:p-10">
      <div className="w-full max-w-6xl bg-white rounded-3xl shadow-2xl overflow-hidden backdrop-blur-sm border border-white/20">
        <div className="flex flex-col lg:flex-row min-h-[600px]">
          {/* Left Side - Branding */}
          <div className="lg:w-1/2 bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 p-12 flex flex-col items-center justify-center relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-20">
              <div className="w-full h-full bg-gradient-to-br from-white/30 via-transparent to-white/10"></div>
              {/* Decorative circles */}
              <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute bottom-20 right-10 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
            </div>

            {/* Logo */}
            <div className="relative z-10 text-center">
              <div className="mb-8">
                {/* Vista Guard Logo */}
                <div className="flex items-center justify-center mb-8">
                  <div className="relative">
                    {/* Logo Icon with enhanced styling */}
                    <div className="flex items-center p-4 bg-white/20 rounded-2xl backdrop-blur-sm border border-white/30">
                      <img className='h-16 w-auto filter brightness-0 invert' src="./logo.png" alt="logo" />
                    </div>
                  </div>
                </div>

                {/* Brand Name */}
                <h1 className="text-4xl lg:text-5xl font-heading font-bold text-white mb-6 drop-shadow-lg">
                  VistaGuard
                </h1>

                {/* Tagline */}
                <p className="text-lg text-white/90 font-medium leading-relaxed max-w-sm mx-auto">
                  Secure Your Vision, Unlock Your Future.
                </p>

                {/* Additional features list */}
                <div className="mt-8 space-y-3">
                  <div className="flex items-center justify-center text-white/80 text-sm">
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Real-time monitoring
                  </div>
                  <div className="flex items-center justify-center text-white/80 text-sm">
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Advanced analytics
                  </div>
                  <div className="flex items-center justify-center text-white/80 text-sm">
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Secure access control
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Login Form */}
          <div className="lg:w-1/2 p-8 lg:p-12 flex flex-col justify-center bg-gradient-to-br from-white to-gray-50">
            <div className="w-full max-w-md mx-auto">
              {/* Header */}
              <div className="text-center mb-8">
                {/* Small Logo for mobile */}
                <div className="mb-6 lg:hidden">
                  <div className="flex items-center justify-center">
                    <div className="p-3 bg-primary-100 rounded-xl">
                      <img className='h-12 w-auto' src="./login-logo.png" alt="logo" />
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <h2 className="text-3xl lg:text-4xl font-heading font-bold text-gray-900 mb-3">
                    Welcome Back!
                  </h2>
                  <p className="text-gray-600 font-body text-lg">
                    Sign in to your VistaGuard account
                  </p>
                </div>

                {/* Progress indicator */}
                <div className="flex items-center justify-center space-x-2 mb-6">
                  <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                  <div className="w-8 h-1 bg-primary-200 rounded-full"></div>
                  <div className="w-2 h-2 bg-primary-200 rounded-full"></div>
                </div>
              </div>

              {/* Login Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Email Field */}
                <div className="space-y-2">
                  <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                      </svg>
                    </div>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 transition-all duration-200 font-body placeholder-gray-400 outline-none bg-white shadow-sm hover:shadow-md ${
                        fieldErrors.email
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                          : 'border-gray-300 focus:ring-primary-500 focus:border-primary-500'
                      }`}
                      required
                    />
                  </div>
                  {fieldErrors.email && (
                    <p className="mt-1 text-sm text-red-600 font-body">
                      {fieldErrors.email}
                    </p>
                  )}
                </div>

                {/* Password Field */}
                <div className="space-y-2">
                  <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-2">
                    Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                    <input
                      type="password"
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder="Enter your password"
                      className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 transition-all duration-200 font-body placeholder-gray-400 outline-none bg-white shadow-sm hover:shadow-md ${
                        fieldErrors.password
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                          : 'border-gray-300 focus:ring-primary-500 focus:border-primary-500'
                      }`}
                      required
                    />
                  </div>
                  {fieldErrors.password && (
                    <p className="mt-1 text-sm text-red-600 font-body">
                      {fieldErrors.password}
                    </p>
                  )}
                </div>

                {/* Remember Me and Forgot Password */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      id="remember-me"
                      name="remember-me"
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700 font-body">
                      Remember me
                    </label>
                  </div>
                  <Link
                    to="/forgot-password"
                    className="text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200 hover:underline"
                  >
                    Forgot Password?
                  </Link>
                </div>

                {/* Login Button */}
                <button
                  type="submit"
                  disabled={isSubmitting || isLoading}
                  className="w-full bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 font-body shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none disabled:shadow-md"
                >
                  <span className="flex items-center justify-center">
                    {isSubmitting || isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Signing In...
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                        </svg>
                        Sign In
                      </>
                    )}
                  </span>
                </button>

                {/* Divider */}
                <div className="relative my-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">New to VistaGuard?</span>
                  </div>
                </div>

                {/* Register Link */}
                <div className="text-center">
                  <Link
                    to="/register"
                    className="inline-flex items-center justify-center w-full px-4 py-3 border border-primary-300 rounded-xl text-primary-700 bg-primary-50 hover:bg-primary-100 font-semibold transition-all duration-200 hover:shadow-md"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                    Create New Account
                  </Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
