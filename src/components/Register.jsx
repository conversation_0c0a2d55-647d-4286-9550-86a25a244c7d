
import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { authToasts } from '../utils/toast';

const Register = () => {
  const navigate = useNavigate();
  const { register, isAuthenticated, isLoading, error, clearError } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    surname: '',
    email: '',
    password: '',
    companyCode: '',
    personnelCode: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldErrors, setFieldErrors] = useState({});

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/branches', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // Clear errors when component mounts or form data changes
  useEffect(() => {
    if (error) {
      clearError();
    }
    // Clear field errors when form data changes
    if (Object.keys(fieldErrors).length > 0) {
      setFieldErrors({});
    }
  }, [formData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFieldErrors({}); // Clear previous field errors

    try {
      const result = await register(formData);

      if (result.success) {
        authToasts.registerSuccess(result.user.name);
        navigate('/branches', { replace: true });
      } else {
        
        if (result?.details) {
          // Create field errors object, handling multiple errors per field
          const errors = {};
          result.details.forEach(detail => {
            if (errors[detail.field]) {
              // If field already has an error, combine them
              errors[detail.field] += ` ${detail.message}`;
            } else {
              errors[detail.field] = detail.message;
            }
          });
          console.log(errors);
          setFieldErrors(errors);
          

          // Also show toast with all validation errors
          authToasts.validationError(result.error, result.details);
        } else {
          authToasts.registerError(result.error);
        }
      }
    } catch (error) {
      // Check if it's a validation error
      if (error?.details) {
        // Create field errors object, handling multiple errors per field
        const errors = {};
        error.details.forEach(detail => {
          if (errors[detail.field]) {
            // If field already has an error, combine them
            errors[detail.field] += ` ${detail.message}`;
          } else {
            errors[detail.field] = detail.message;
          }
        });
        setFieldErrors(errors);

        // Also show toast with all validation errors
        authToasts.validationError(error.error, error.details);
      } else {
        authToasts.registerError(error.error);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 flex items-center justify-center p-4 md:p-10">
      <div className="w-full max-w-6xl bg-white rounded-3xl shadow-2xl overflow-hidden backdrop-blur-sm border border-white/20">
        <div className="flex flex-col lg:flex-row min-h-[700px]">
          {/* Left Side - Branding */}
          <div className="lg:w-1/2 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 p-12 flex flex-col items-center justify-center relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-20">
              <div className="w-full h-full bg-gradient-to-br from-white/30 via-transparent to-white/10"></div>
              {/* Decorative elements */}
              <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute bottom-20 right-10 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute bottom-1/3 left-1/4 w-28 h-28 bg-white/10 rounded-full blur-xl"></div>
            </div>

            {/* Logo */}
            <div className="relative z-10 text-center">
              <div className="mb-8">
                {/* Vista Guard Logo */}
                <div className="flex items-center justify-center mb-8">
                  <div className="relative">
                    {/* Logo Icon with enhanced styling */}
                    <div className="flex items-center p-4 bg-white/20 rounded-2xl backdrop-blur-sm border border-white/30">
                      <img className='h-16 w-auto filter brightness-0 invert' src="./logo.png" alt="logo" />
                    </div>
                  </div>
                </div>

                {/* Brand Name */}
                <h1 className="text-4xl lg:text-5xl font-heading font-bold text-white mb-6 drop-shadow-lg">
                  Join VistaGuard
                </h1>

                {/* Tagline */}
                <p className="text-lg text-white/90 font-medium leading-relaxed max-w-sm mx-auto mb-8">
                  Start your journey with advanced security monitoring and analytics.
                </p>

                {/* Benefits list */}
                <div className="space-y-4">
                  <div className="flex items-center text-white/80 text-sm">
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span>Multi-branch monitoring</span>
                  </div>
                  <div className="flex items-center text-white/80 text-sm">
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span>Compliance tracking</span>
                  </div>
                  <div className="flex items-center text-white/80 text-sm">
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span>Instant notifications</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Register Form */}
          <div className="lg:w-1/2 p-8 lg:p-12 flex flex-col justify-center bg-gradient-to-br from-white to-gray-50">
            <div className="w-full max-w-md mx-auto">
              {/* Header */}
              <div className="text-center mb-8">
                {/* Small Logo for mobile */}
                <div className="mb-6 lg:hidden">
                  <div className="flex items-center justify-center">
                    <div className="p-3 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-xl">
                      <img className='h-12 w-auto' src="./logo.png" alt="logo" />
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <h2 className="text-3xl lg:text-4xl font-heading font-bold text-gray-900 mb-3">
                    Create Account
                  </h2>
                  <p className="text-gray-600 font-body text-lg">
                    Join VistaGuard and start monitoring
                  </p>
                </div>

                {/* Progress indicator */}
                <div className="flex items-center justify-center space-x-2 mb-6">
                  <div className="w-2 h-2 bg-indigo-600 rounded-full"></div>
                  <div className="w-8 h-1 bg-indigo-200 rounded-full"></div>
                  <div className="w-2 h-2 bg-indigo-200 rounded-full"></div>
                </div>
              </div>

              {/* Register Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name and Surname Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Name Field */}
                  <div className="space-y-2">
                    <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
                      First Name
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="John"
                        className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 transition-all duration-200 font-body placeholder-gray-400 outline-none bg-white shadow-sm hover:shadow-md ${fieldErrors.name
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                            : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'
                          }`}
                        required
                      />
                    </div>
                    {fieldErrors.name && (
                      <p className="mt-1 text-sm text-red-600 font-body">
                        {fieldErrors.name}
                      </p>
                    )}
                  </div>

                  {/* Surname Field */}
                  <div className="space-y-2">
                    <label htmlFor="surname" className="block text-sm font-semibold text-gray-700 mb-2">
                      Last Name
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <input
                        type="text"
                        id="surname"
                        name="surname"
                        value={formData.surname}
                        onChange={handleInputChange}
                        placeholder="Doe"
                        className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 transition-all duration-200 font-body placeholder-gray-400 outline-none bg-white shadow-sm hover:shadow-md ${fieldErrors.surname
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                            : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'
                          }`}
                        required
                      />
                    </div>
                    {fieldErrors.surname && (
                      <p className="mt-1 text-sm text-red-600 font-body">
                        {fieldErrors.surname}
                      </p>
                    )}
                  </div>
                </div>

                {/* Email Field */}
                <div className="space-y-2">
                  <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                      </svg>
                    </div>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 transition-all duration-200 font-body placeholder-gray-400 outline-none bg-white shadow-sm hover:shadow-md ${fieldErrors.email
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                          : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'
                        }`}
                      required
                    />
                  </div>
                  {fieldErrors.email && (
                    <p className="mt-1 text-sm text-red-600 font-body">
                      {fieldErrors.email}
                    </p>
                  )}
                </div>

                {/* Password Field */}
                <div className="space-y-2">
                  <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-2">
                    Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                    <input
                      type="password"
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder="Create a strong password"
                      className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 transition-all duration-200 font-body placeholder-gray-400 outline-none bg-white shadow-sm hover:shadow-md ${fieldErrors.password
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                          : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'
                        }`}
                      required
                    />
                  </div>
                  {fieldErrors.password && (
                    <p className="mt-1 text-sm text-red-600 font-body">
                      {fieldErrors.password}
                    </p>
                  )}
                </div>

                {/* Company Code and Personnel Code Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Company Code Field */}
                  <div className="space-y-2">
                    <label htmlFor="companyCode" className="block text-sm font-semibold text-gray-700 mb-2">
                      Company Code
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                      </div>
                      <input
                        type="text"
                        id="companyCode"
                        name="companyCode"
                        value={formData.companyCode}
                        onChange={handleInputChange}
                        placeholder="COMP001"
                        className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 transition-all duration-200 font-body placeholder-gray-400 outline-none bg-white shadow-sm hover:shadow-md ${fieldErrors.companyCode
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                            : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'
                          }`}
                        required
                      />
                    </div>
                    {fieldErrors.companyCode && (
                      <p className="mt-1 text-sm text-red-600 font-body">
                        {fieldErrors.companyCode}
                      </p>
                    )}
                  </div>

                  {/* Personnel Code Field */}
                  <div className="space-y-2">
                    <label htmlFor="personnelCode" className="block text-sm font-semibold text-gray-700 mb-2">
                      Personnel Code
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                        </svg>
                      </div>
                      <input
                        type="text"
                        id="personnelCode"
                        name="personnelCode"
                        value={formData.personnelCode}
                        onChange={handleInputChange}
                        placeholder="EMP001"
                        className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 transition-all duration-200 font-body placeholder-gray-400 outline-none bg-white shadow-sm hover:shadow-md ${fieldErrors.personnelCode
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                            : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'
                          }`}
                        required
                      />
                    </div>
                    {fieldErrors.personnelCode && (
                      <p className="mt-1 text-sm text-red-600 font-body">
                        {fieldErrors.personnelCode}
                      </p>
                    )}
                  </div>
                </div>

                {/* Register Button */}
                <button
                  type="submit"
                  disabled={isSubmitting || isLoading}
                  className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-body shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none disabled:shadow-md"
                >
                  <span className="flex items-center justify-center">
                    {isSubmitting || isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating Account...
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                        </svg>
                        Create Account
                      </>
                    )}
                  </span>
                </button>

                {/* Terms and Privacy */}
                <div className="text-center">
                  <p className="text-sm text-gray-600 font-body leading-relaxed">
                    By creating an account, you agree to our{' '}
                    <a href="#" className="text-indigo-600 hover:text-indigo-700 font-medium transition-colors duration-200 hover:underline">
                      Terms of Service
                    </a>{' '}
                    and{' '}
                    <a href="#" className="text-indigo-600 hover:text-indigo-700 font-medium transition-colors duration-200 hover:underline">
                      Privacy Policy
                    </a>
                  </p>
                </div>

                {/* Divider */}
                <div className="relative my-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Already have an account?</span>
                  </div>
                </div>

                {/* Login Link */}
                <div className="text-center">
                  <Link
                    to="/login"
                    className="inline-flex items-center justify-center w-full px-4 py-3 border border-indigo-300 rounded-xl text-indigo-700 bg-indigo-50 hover:bg-indigo-100 font-semibold transition-all duration-200 hover:shadow-md"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    Sign In Instead
                  </Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
