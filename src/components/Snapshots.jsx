import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import DashboardLayout from './DashboardLayout';
import StatisticsCards from './StatisticsCards';
import SnapshotGrid from './SnapshotGrid';
import { useAuth } from '../context/AuthContext';
import { dashboardAPI } from '../services/api';
import { showToast } from '../utils/toast';

const Snapshots = () => {
  // Get user data from authentication context and branch ID from URL
  const { user } = useAuth();
  const { id: branchId } = useParams();

  // State management
  const [snapshotsData, setSnapshotsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Filters and pagination state
  const [filters, setFilters] = useState({
    type: '',
    sortBy: 'newest',
    page: 1,
    limit: 10
  });

  // Fetch snapshots data from API
  const fetchSnapshots = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const queryParams = {};
      if (filters.type) queryParams.type = filters.type;
      if (filters.sortBy) queryParams.sortBy = filters.sortBy;
      if (filters.page) queryParams.page = filters.page;
      if (filters.limit) queryParams.limit = filters.limit;

      const response = await dashboardAPI.getBranchSnapshots(branchId, queryParams);

      if (response.success && response.data) {
        setSnapshotsData(response.data);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching snapshots:', err);
      setError(err.message || 'Failed to load snapshots');
      if (!isRefresh) {
        showToast.error('Failed to load snapshots. Please try again.');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Initial fetch and refetch when filters change
  useEffect(() => {
    if (branchId) {
      fetchSnapshots();
    }
  }, [branchId, filters]);

  // Handle refresh button
  const handleRefresh = () => {
    fetchSnapshots(true);
  };

  // Loading state
  if (loading) {
    return (
      <DashboardLayout user={user}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600 font-body">Loading snapshots...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <DashboardLayout user={user}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to load snapshots</h3>
            <p className="text-gray-600 font-body mb-4">{error}</p>
            <button
              onClick={() => fetchSnapshots()}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Extract data for display
  const branchName = snapshotsData?.snapshots?.[0]?.branch?.name || 'Unknown Branch';
  const stats = snapshotsData?.stats || {};

  return (
    <DashboardLayout user={user}>
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center flex-wrap gap-5 justify-between">
          <div>
            <h1 className="text-3xl font-heading font-bold text-gray-900 mb-2">
              Branch Snapshots
            </h1>
            <div className="flex items-center space-x-2">
              <h2 className="text-xl font-heading font-semibold text-primary-600">
                Snapshots for {branchName}
              </h2>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Active
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-3 md:justify-end justify-center ">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-body disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <StatisticsCards
        totalSnapshots={stats.totalSnapshots || 0}
        compliantSnapshots={stats.nonComplaintSnapshots || 0}
        nonCompliantSnapshots={stats.complaintSnapshots || 0}
        overallComplianceRate={stats.overallComplianceRate || 0}
      />

      {/* Snapshots Grid */}
      <SnapshotGrid
        snapshotsData={snapshotsData}
        filters={filters}
        setFilters={setFilters}
        branchId={branchId}
      />
    </DashboardLayout>
  );
};

export default Snapshots;
