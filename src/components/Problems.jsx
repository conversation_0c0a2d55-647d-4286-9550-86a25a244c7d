import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from './DashboardLayout';
import { dashboardAPI } from '../services/api';
import { showToast } from '../utils/toast';


const Problems = () => {
  const navigate = useNavigate();

  // State management
  const [flaggedReports, setFlaggedReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({ total: 0, high: 0, medium: 0, low: 0 });
  const [pagination, setPagination] = useState({});

  // Filter and pagination state
  const [filters, setFilters] = useState({
    priority: '',
    sortBy: 'newest',
    page: 1,
    limit: 12
  });

  // Fetch flagged reports from API
  const fetchFlaggedReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await dashboardAPI.getFlaggedReports(filters);

      if (response.success) {
        setFlaggedReports(response.data.reports);
        setStats(response.data.stats);
        setPagination(response.data.pagination);
      } else {
        throw new Error(response.message || 'Failed to fetch flagged reports');
      }
    } catch (err) {
      console.error('Error fetching flagged reports:', err);
      setError(err.message);
      showToast.error(err.message || 'Failed to load flagged reports');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and when filters change
  useEffect(() => {
    fetchFlaggedReports();
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    const newFilters = { ...filters, [filterType]: value, page: 1 };
    setFilters(newFilters);
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    const newFilters = { ...filters, page: newPage };
    setFilters(newFilters);
  };

  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get priority badge styling
  const getPriorityBadge = (priority) => {
    const priorityStyles = {
      HIGH: 'bg-red-100 text-red-800',
      MEDIUM: 'bg-yellow-100 text-yellow-800',
      LOW: 'bg-blue-100 text-blue-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityStyles[priority] || 'bg-gray-100 text-gray-800'}`}>
        {priority}
      </span>
    );
  };

  // Handle clicking on a problem to view snapshot details
  const handleProblemClick = (report) => {
    navigate(`/snapshot/${report.snapshot.id}`);
  };

  // Loading state
  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading flagged reports...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Reports</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={fetchFlaggedReports}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 font-heading">Problems</h1>
            <p className="text-gray-600 font-body mt-2">
              Issues detected across your branches that require attention
            </p>
          </div>

          {/* Stats Cards */}
          <div className="flex flex-wrap gap-4">
            <div className="bg-red-50 border border-red-200 rounded-lg px-4 py-3">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-sm font-medium text-red-700">High Priority</span>
                <span className="text-sm font-bold text-red-900">{stats.high}</span>
              </div>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg px-4 py-3">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-sm font-medium text-yellow-700">Medium Priority</span>
                <span className="text-sm font-bold text-yellow-900">{stats.medium}</span>
              </div>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-3">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-blue-700">Low Priority</span>
                <span className="text-sm font-bold text-blue-900">{stats.low}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
            <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
              <h3 className="text-lg font-semibold text-gray-900 font-heading">Filter Problems</h3>

              <div className="flex flex-wrap gap-3 items-center">
                <div className="flex min-w-60 items-center space-x-2 bg-gray-50 rounded-lg px-3 py-2">
                  <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
                  </svg>
                  <select
                    value={filters.priority}
                    onChange={(e) => handleFilterChange('priority', e.target.value)}
                    className="bg-transparent border-none text-sm font-medium text-gray-700 font-body focus:ring-0 focus:outline-none cursor-pointer"
                  >
                    <option value="">All Priorities</option>
                    <option value="HIGH">High Priority</option>
                    <option value="MEDIUM">Medium Priority</option>
                    <option value="LOW">Low Priority</option>
                </select>
              </div>

              <div className="flex min-w-60 items-center space-x-2 bg-gray-50 rounded-lg px-3 py-2">
                <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                </svg>
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  className="bg-transparent border-none text-sm font-medium text-gray-700 font-body focus:ring-0 focus:outline-none cursor-pointer"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="priority">Priority First</option>
                </select>
              </div>
            </div>
          </div>

          {/* Report count */}
          <div className="text-sm text-gray-600 font-body">
            <span className="font-medium text-gray-900">{flaggedReports.length}</span> flagged reports
          </div>
        </div>
      </div>

        {/* Empty state */}
        {flaggedReports.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📋</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Flagged Reports</h3>
            <p className="text-gray-600">
              {filters.priority ? `No ${filters.priority.toLowerCase()} priority reports found.` : 'All reports are currently resolved.'}
            </p>
          </div>
        ) : (
          <>
            {/* Problems Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
              {flaggedReports.map((report) => (
                <div
                  key={report.id}
                  onClick={() => handleProblemClick(report)}
                  className="group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:border-red-200 transition-all duration-300 cursor-pointer"
                >
                  {/* Problem Image */}
                  <div className="relative overflow-hidden">
                    <img
                      src={report.snapshot.image}
                      alt={`${report.snapshot.branch.name} - ${report.snapshot.type}`}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    {/* Overlay gradient */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    {/* Priority badge */}
                    <div className="absolute top-3 left-3">
                      {getPriorityBadge(report.priority)}
                    </div>

                    {/* Quick action overlay */}
                    <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <button className="text-white text-sm font-medium hover:text-red-200 transition-colors duration-200">
                        View Details →
                      </button>
                    </div>
                  </div>

                  {/* Report Info */}
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="text-sm font-semibold text-gray-900 font-body group-hover:text-red-600 transition-colors duration-200">
                        {report.snapshot.branch.name}
                      </h4>
                      <button className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 p-1 hover:bg-gray-100 rounded">
                        <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                        </svg>
                      </button>
                    </div>

                    <div className="space-y-2">
                      <p className="text-xs text-gray-500 font-body">
                        {formatTimestamp(report.createdAt)}
                      </p>
                      <p className="text-xs text-gray-600 font-body">
                        {report.snapshot.branch.city}
                      </p>
                      {report.details && (
                        <p className="text-xs text-gray-700 font-body line-clamp-2">
                          {report.details}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="mt-8 flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span>
                    Showing {((pagination.currentPage - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.currentPage * pagination.limit, pagination.totalCount)} of{' '}
                    {pagination.totalCount} reports
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={!pagination.hasPrevPage}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${page === pagination.currentPage
                            ? 'bg-primary-600 text-white'
                            : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                          }`}
                      >
                        {page}
                      </button>
                    ))}
                  </div>

                  <button
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={!pagination.hasNextPage}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Problems;
