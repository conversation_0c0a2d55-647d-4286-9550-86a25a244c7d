import React from 'react';

const StatisticsCards = ({ 
  totalSnapshots = 12, 
  compliantSnapshots = 7, 
  nonCompliantSnapshots = 5, 
  overallComplianceRate = 58.3 
}) => {
  const statistics = [
    {
      title: "Total Snapshots",
      value: totalSnapshots,
      description: "All recorded snapshots for this branch.",
      icon: (
        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
        </svg>
      ),
      bgColor: "bg-blue-50",
      iconBg: "bg-blue-100"
    },
    {
      title: "Compliant Snapshots",
      value: compliantSnapshots,
      description: `${((compliantSnapshots / totalSnapshots) * 100).toFixed(1)}% compliant inspections.`,
      icon: (
        <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: "bg-green-50",
      iconBg: "bg-green-100"
    },
    {
      title: "Non-Compliant Snapshots",
      value: nonCompliantSnapshots,
      description: `${((nonCompliantSnapshots / totalSnapshots) * 100).toFixed(1)}% non-compliant inspections.`,
      icon: (
        <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: "bg-red-50",
      iconBg: "bg-red-100"
    },
    {
      title: "Overall Compliance Rate",
      value: `${overallComplianceRate}%`,
      description: "Weighted average compliance score.",
      icon: (
        <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      bgColor: "bg-purple-50",
      iconBg: "bg-purple-100"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {statistics.map((stat, index) => (
        <div key={index} className={`group ${stat.bgColor} rounded-xl p-6 border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-300 cursor-pointer relative overflow-hidden`}>
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-20 h-20 opacity-10 transform translate-x-6 -translate-y-6">
            <div className={`w-full h-full ${stat.iconBg} rounded-full`}></div>
          </div>

          <div className="relative">
            <div className="flex items-start justify-between mb-4">
              <div className={`${stat.iconBg} p-3 rounded-xl shadow-sm group-hover:shadow-md transition-shadow duration-300`}>
                {stat.icon}
              </div>

              {/* Trend indicator */}
              <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                </svg>
                <span className="text-xs font-medium text-green-600">+2.5%</span>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-semibold text-gray-700 font-body">
                {stat.title}
              </h3>

              <div className="flex items-baseline space-x-2">
                <p className="text-3xl font-bold text-gray-900 font-heading group-hover:scale-105 transition-transform duration-300">
                  {stat.value}
                </p>
                {index === 3 && (
                  <div className="flex items-center">
                    <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-purple-500 to-purple-600 rounded-full transition-all duration-500"
                        style={{ width: `${parseFloat(stat.value)}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>

              <p className="text-xs text-gray-600 font-body leading-relaxed">
                {stat.description}
              </p>

              {/* Action button */}
              <div className="pt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <button className="text-xs font-medium text-gray-500 hover:text-gray-700 transition-colors duration-200 flex items-center space-x-1">
                  <span>View details</span>
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default StatisticsCards;
