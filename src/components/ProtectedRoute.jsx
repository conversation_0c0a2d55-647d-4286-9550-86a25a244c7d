import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

// Loading spinner component
const LoadingSpinner = () => (
  <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 flex items-center justify-center">
    <div className="text-center">
      {/* Vista Guard Logo */}
      <div className="mb-8">
        <div className="flex items-center justify-center mb-6">
          <div className="relative">
            <div className="flex items-center p-4 bg-primary-100 rounded-2xl">
              <img className='h-16 w-auto' src="/logo.png" alt="Vista Guard" />
            </div>
          </div>
        </div>
        <h2 className="text-2xl font-heading font-bold text-gray-900 mb-2">
          VistaGuard
        </h2>
        <p className="text-gray-600 font-body">
          Loading your secure workspace...
        </p>
      </div>

      {/* Loading Spinner */}
      <div className="flex items-center justify-center">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-primary-200 rounded-full animate-spin"></div>
          <div className="absolute top-0 left-0 w-12 h-12 border-4 border-transparent border-t-primary-600 rounded-full animate-spin"></div>
        </div>
      </div>

      {/* Loading text */}
      <div className="mt-6">
        <div className="flex items-center justify-center space-x-1">
          <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <p className="text-sm text-gray-500 mt-2 font-body">
          Verifying authentication...
        </p>
      </div>
    </div>
  </div>
);

// ProtectedRoute component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    // Save the attempted location for redirect after login
    return (
      <Navigate 
        to="/login" 
        state={{ from: location }} 
        replace 
      />
    );
  }

  // User is authenticated, render the protected component
  return children;
};

export default ProtectedRoute;
