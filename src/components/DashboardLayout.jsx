import React from 'react';
import { DashboardHeader } from './Layout';
import DashboardFooter from './DashboardFooter';

const DashboardLayout = ({ children, user }) => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <DashboardHeader user={user} />
      
      {/* Main Content */}
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {children}
        </div>
      </main>
      
      {/* Footer */}
      <DashboardFooter />
    </div>
  );
};

export default DashboardLayout;
