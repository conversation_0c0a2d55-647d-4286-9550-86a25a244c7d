import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { authToasts } from '../utils/toast';

const Layout = ({ children, className = "" }) => {
  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {children}
    </div>
  );
};

// Header Component for authenticated pages
export const Header = ({ title, subtitle, actions }) => {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          <div>
            {title && (
              <h1 className="text-2xl font-heading font-bold text-gray-900">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="mt-1 text-sm text-gray-600 font-body">
                {subtitle}
              </p>
            )}
          </div>
          {actions && (
            <div className="flex items-center space-x-4">
              {actions}
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

// Sidebar Component for navigation
export const Sidebar = ({ children, isOpen = true }) => {
  return (
    <aside className={`bg-white shadow-sm border-r border-gray-200 transition-all duration-300 ${isOpen ? 'w-64' : 'w-16'
      }`}>
      <div className="h-full flex flex-col">
        {/* Logo Section */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center">
            {/* Vista Guard Logo */}
            <div className="flex items-center space-x-1">
              <div className="w-4 h-6 bg-gradient-to-b from-pink-400 to-pink-500 rounded transform -rotate-12"></div>
              <div className="w-4 h-6 bg-gradient-to-b from-teal-400 to-teal-500 rounded transform -translate-x-1"></div>
              <div className="w-4 h-6 bg-gradient-to-b from-primary-500 to-primary-600 rounded transform -translate-x-2 rotate-12"></div>
            </div>
            {isOpen && (
              <span className="ml-3 text-xl font-heading font-bold text-gray-900">
                VistaGuard
              </span>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          {children}
        </nav>
      </div>
    </aside>
  );
};

// Main Content Area
export const MainContent = ({ children, className = "" }) => {
  return (
    <main className={`flex-1 overflow-auto ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </div>
    </main>
  );
};

// Card Component for content sections
export const Card = ({ children, className = "", title, subtitle, actions }) => {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {(title || subtitle || actions) && (
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div>
              {title && (
                <h3 className="text-lg font-heading font-semibold text-gray-900">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="mt-1 text-sm text-gray-600 font-body">
                  {subtitle}
                </p>
              )}
            </div>
            {actions && (
              <div className="flex items-center space-x-2">
                {actions}
              </div>
            )}
          </div>
        </div>
      )}
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};

// Button Component
export const Button = ({
  children,
  variant = "primary",
  size = "md",
  className = "",
  disabled = false,
  ...props
}) => {
  const baseClasses = "inline-flex items-center justify-center font-medium rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-body";

  const variants = {
    primary: "bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500",
    secondary: "bg-gray-100 hover:bg-gray-200 text-gray-900 focus:ring-gray-500",
    outline: "border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-primary-500",
    danger: "bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",
  };

  const sizes = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-sm",
    lg: "px-6 py-3 text-base",
  };

  return (
    <button
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
};

// Input Component
export const Input = ({
  label,
  error,
  className = "",
  containerClassName = "",
  ...props
}) => {
  return (
    <div className={containerClassName}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2 font-body">
          {label}
        </label>
      )}
      <input
        className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 font-body ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''
          } ${className}`}
        {...props}
      />
      {error && (
        <p className="mt-1 text-sm text-red-600 font-body">{error}</p>
      )}
    </div>
  );
};

// Dashboard Header Component
export const DashboardHeader = ({ user = { name: "John Doe" } }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const userMenuRef = useRef(null);

  // Get active link from current path
  const getActiveLink = () => {
    const path = location.pathname;
    if (path === '/branches') return 'branches';
    if (path === '/problems') return 'problems';
    return '#';
  };

  const activeLink = getActiveLink();

  // Close mobile menu when clicking on a link
  const handleMobileMenuClose = () => {
    setIsMobileMenuOpen(false);
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      authToasts.logoutSuccess();
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('Logout error:', error);
      authToasts.error('Error logging out. Please try again.');
    }
  };

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setIsUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Left Side - Logo and Navigation */}
          <div className="flex items-center space-x-8">
            {/* VistaGuard Logo */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-1">
                <img className='h-10 w-auto' src="/logo.png" alt="VistaGuard Logo" />
              </div>
              <span className="text-xl font-heading font-bold text-gray-900">
                VistaGuard
              </span>
            </div>

            {/* Desktop Navigation Menu */}
            <nav className="hidden md:flex items-center space-x-6">
              <Link
                to="/branches"
                className={`text-gray-900 font-medium font-body hover:text-primary-600 transition-colors duration-200 pb-1 ${activeLink === "branches" ? 'border-b-2 border-primary-600 text-primary-600' : ''}`}
              >
                Branches
              </Link>
              <Link
                to="/problems"
                className={`text-gray-900 font-medium font-body hover:text-primary-600 transition-colors duration-200 pb-1 ${activeLink === "problems" ? 'border-b-2 border-primary-600 text-primary-600' : ''}`}
              >
                Problems
              </Link>
            </nav>
          </div>

          {/* Right Side - User Profile and Mobile Menu Button */}
          <div className="flex items-center space-x-4">
            {/* User Profile Dropdown */}
            <div className="relative" ref={userMenuRef}>
              <button
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium font-body">
                    {user.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <span className="hidden sm:block text-sm font-medium text-gray-700 font-body">
                  {user.name}
                </span>
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Dropdown Menu */}
              {isUserMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                  <div className="px-4 py-2 border-b border-gray-100">
                    <p className="text-sm font-medium text-gray-900 font-body">{user.name}</p>
                    <p className="text-xs text-gray-500 font-body">{user.email}</p>
                  </div>
                  <button
                    onClick={handleLogout}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 font-body flex items-center space-x-2 transition-colors duration-200"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    <span>Sign Out</span>
                  </button>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors duration-200"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              {/* Hamburger icon */}
              <svg
                className={`${isMobileMenuOpen ? 'hidden' : 'block'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              {/* Close icon */}
              <svg
                className={`${isMobileMenuOpen ? 'block' : 'hidden'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        <div className={`md:hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-64 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
          <div className="px-2 pt-2 pb-3 space-y-1 bg-gray-50 rounded-lg mb-4">
            <Link
              to="/branches"
              onClick={handleMobileMenuClose}
              className={`block px-3 py-2 rounded-md text-base font-medium font-body transition-colors duration-200 ${activeLink === "branches"
                  ? 'text-primary-600 bg-primary-50 border-l-4 border-primary-600'
                  : 'text-gray-700 hover:text-primary-600 hover:bg-gray-100'
                }`}
            >
              Branches
            </Link>
            <Link
              to="/problems"
              onClick={handleMobileMenuClose}
              className={`block px-3 py-2 rounded-md text-base font-medium font-body transition-colors duration-200 ${activeLink === "problems"
                  ? 'text-primary-600 bg-primary-50 border-l-4 border-primary-600'
                  : 'text-gray-700 hover:text-primary-600 hover:bg-gray-100'
                }`}
            >
              Problems
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Layout;
