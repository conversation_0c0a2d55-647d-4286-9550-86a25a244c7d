import toast from 'react-hot-toast';

// Custom toast configuration with Vista Guard styling
const toastConfig = {
  // Default options for all toasts
  duration: 4000,
  position: 'top-right',
  
  // Custom styling
  style: {
    borderRadius: '12px',
    background: '#fff',
    color: '#374151',
    fontSize: '14px',
    fontWeight: '500',
    padding: '16px 20px',
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    border: '1px solid #e5e7eb',
    maxWidth: '400px',
  },

  // Success toast styling
  success: {
    style: {
      background: '#f0fdf4',
      color: '#166534',
      border: '1px solid #bbf7d0',
    },
    iconTheme: {
      primary: '#16a34a',
      secondary: '#f0fdf4',
    },
  },

  // Error toast styling
  error: {
    style: {
      background: '#fef2f2',
      color: '#991b1b',
      border: '1px solid #fecaca',
    },
    iconTheme: {
      primary: '#dc2626',
      secondary: '#fef2f2',
    },
  },

  // Loading toast styling
  loading: {
    style: {
      background: '#f8fafc',
      color: '#475569',
      border: '1px solid #e2e8f0',
    },
    iconTheme: {
      primary: '#3b82f6',
      secondary: '#f8fafc',
    },
  },
};

// Custom toast functions with Vista Guard branding
export const showToast = {
  // Success toast
  success: (message, options = {}) => {
    return toast.success(message, {
      ...toastConfig,
      ...toastConfig.success,
      ...options,
    });
  },

  // Error toast
  error: (message, options = {}) => {
    return toast.error(message, {
      ...toastConfig,
      ...toastConfig.error,
      ...options,
    });
  },

  // Loading toast
  loading: (message, options = {}) => {
    return toast.loading(message, {
      ...toastConfig,
      ...toastConfig.loading,
      ...options,
    });
  },

  // Info toast (custom)
  info: (message, options = {}) => {
    return toast(message, {
      ...toastConfig,
      icon: 'ℹ️',
      style: {
        ...toastConfig.style,
        background: '#eff6ff',
        color: '#1e40af',
        border: '1px solid #bfdbfe',
      },
      ...options,
    });
  },

  // Warning toast (custom)
  warning: (message, options = {}) => {
    return toast(message, {
      ...toastConfig,
      icon: '⚠️',
      style: {
        ...toastConfig.style,
        background: '#fffbeb',
        color: '#92400e',
        border: '1px solid #fed7aa',
      },
      ...options,
    });
  },

  // Custom toast with custom icon
  custom: (message, icon, options = {}) => {
    return toast(message, {
      ...toastConfig,
      icon,
      ...options,
    });
  },

  // Dismiss specific toast
  dismiss: (toastId) => {
    toast.dismiss(toastId);
  },

  // Dismiss all toasts
  dismissAll: () => {
    toast.dismiss();
  },

  // Promise toast for async operations
  promise: (promise, messages, options = {}) => {
    return toast.promise(
      promise,
      {
        loading: messages.loading || 'Loading...',
        success: messages.success || 'Success!',
        error: messages.error || 'Something went wrong!',
      },
      {
        ...toastConfig,
        success: {
          ...toastConfig.success,
          duration: 3000,
        },
        error: {
          ...toastConfig.error,
          duration: 5000,
        },
        ...options,
      }
    );
  },
};

// Authentication specific toast messages
export const authToasts = {
  loginSuccess: (userName) => {
    showToast.success(`Welcome back, ${userName}! 🎉`, {
      duration: 3000,
    });
  },

  loginError: (error) => {
    showToast.error(error || 'Login failed. Please check your credentials.', {
      duration: 5000,
    });
  },

  registerSuccess: (userName) => {
    showToast.success(`Account created successfully! Welcome to VistaGuard, ${userName}! 🚀`, {
      duration: 4000,
    });
  },

  registerError: (error) => {
    showToast.error(error || 'Registration failed. Please try again.', {
      duration: 5000,
    });
  },

  logoutSuccess: () => {
    showToast.success('Logged out successfully. See you soon! 👋', {
      duration: 3000,
    });
  },

  sessionExpired: () => {
    showToast.warning('Your session has expired. Please log in again.', {
      duration: 5000,
    });
  },

  networkError: () => {
    showToast.error('Network error. Please check your connection and try again.', {
      duration: 6000,
    });
  },

  validationError: (message, validationDetails = null) => {
    if (validationDetails && Array.isArray(validationDetails)) {
      // Show detailed validation errors
      const errorList = validationDetails.map(detail => `• ${detail.message}`).join('\n');
      showToast.error(`${message}\n\n${errorList}`, {
        duration: 6000,
        style: {
          whiteSpace: 'pre-line',
          maxWidth: '500px',
        },
      });
    } else {
      showToast.error(message || 'Please check your input and try again.', {
        duration: 4000,
      });
    }
  },
};

// Export default toast for direct usage
export default showToast;
