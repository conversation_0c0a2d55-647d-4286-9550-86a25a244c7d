
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { AuthProvider } from './context/AuthContext'
import Login from './components/Login'
import Register from './components/Register'
import ForgotPassword from './components/ForgotPassword'
import Branches from './components/Branches'
import Problems from './components/Problems'
import SnapshotDetails from './components/SnapshotDetails'
import Snapshots from './components/Snapshots'
import Layout from './components/Layout'
import ProtectedRoute from './components/ProtectedRoute'
import ErrorBoundary from './components/ErrorBoundary'

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <Router>
          <Routes>
            {/* Authentication Routes */}
            <Route path="/login" element={
              <Layout>
                <Login />
              </Layout>
            } />
            <Route path="/register" element={
              <Layout>
                <Register />
              </Layout>
            } />
            <Route path="/forgot-password" element={
              <Layout>
                <ForgotPassword />
              </Layout>
            } />

            {/* Protected Dashboard Routes */}
            <Route path="/branches" element={
              <ProtectedRoute>
                <Branches />
              </ProtectedRoute>
            } />
            <Route path="/problems" element={
              <ProtectedRoute>
                <Problems />
              </ProtectedRoute>
            } />
            <Route path="/snapshot/:id" element={
              <ProtectedRoute>
                <SnapshotDetails />
              </ProtectedRoute>
            } />
            <Route path="/snapshots/:id" element={
              <ProtectedRoute>
                <Snapshots />
              </ProtectedRoute>
            } />

            {/* Default Route */}
            <Route path="/" element={<Navigate to="/branches" replace />} />
          </Routes>

          {/* Toast Notifications */}
          <Toaster />
        </Router>
      </AuthProvider>
    </ErrorBoundary>
  )
}

export default App
