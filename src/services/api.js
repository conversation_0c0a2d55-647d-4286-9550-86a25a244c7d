// API Configuration and Service Functions for Vista Guard

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Helper function to get auth token from localStorage
const getAuthToken = () => {
  return localStorage.getItem('vista_guard_token');
};

// Helper function to create headers with auth token
const createHeaders = (includeAuth = false) => {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (includeAuth) {
    const token = getAuthToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }
  
  return headers;
};

// Generic API request function with error handling
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...createHeaders(options.includeAuth),
        ...options.headers,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle different types of API errors
      if (data.error) {
        // Create error object with validation details if available
        const error = new Error(data.message || 'An error occurred');
        error.details = data.details || null;
        error.isValidationError = data.message === 'Validation failed' && data.details;
        throw error;
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return data;
  } catch (error) {
    // Network or parsing errors
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('Network error. Please check your connection and try again.');
    }
    throw error;
  }
};

// Authentication API functions
export const authAPI = {
  // User Registration
  register: async (userData) => {
    return await apiRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  },

  // User Login
  login: async (credentials) => {
    return await apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  },

  // Get User Profile
  getProfile: async () => {
    return await apiRequest('/auth/profile', {
      method: 'GET',
      includeAuth: true,
    });
  },

  // User Logout
  logout: async () => {
    return await apiRequest('/auth/logout', {
      method: 'POST',
      includeAuth: true,
    });
  },
};

// Token management functions
export const tokenManager = {
  // Store token in localStorage
  setToken: (token) => {
    localStorage.setItem('vista_guard_token', token);
  },

  // Get token from localStorage
  getToken: () => {
    return localStorage.getItem('vista_guard_token');
  },

  // Remove token from localStorage
  removeToken: () => {
    localStorage.removeItem('vista_guard_token');
  },

  // Check if token exists
  hasToken: () => {
    return !!localStorage.getItem('vista_guard_token');
  },
};

// User data management functions
export const userManager = {
  // Store user data in localStorage
  setUser: (userData) => {
    localStorage.setItem('vista_guard_user', JSON.stringify(userData));
  },

  // Get user data from localStorage
  getUser: () => {
    const userData = localStorage.getItem('vista_guard_user');
    return userData ? JSON.parse(userData) : null;
  },

  // Remove user data from localStorage
  removeUser: () => {
    localStorage.removeItem('vista_guard_user');
  },

  // Check if user data exists
  hasUser: () => {
    return !!localStorage.getItem('vista_guard_user');
  },
};

// Dashboard API functions
export const dashboardAPI = {
  // Get All Branches
  getBranches: async () => {
    return await apiRequest('/branches', {
      method: 'GET',
      includeAuth: true,
    });
  },

  // Get Branch Snapshots
  getBranchSnapshots: async (branchId, queryParams = {}) => {
    const searchParams = new URLSearchParams();

    // Add query parameters if provided
    if (queryParams.type) searchParams.append('type', queryParams.type);
    if (queryParams.sortBy) searchParams.append('sortBy', queryParams.sortBy);
    if (queryParams.page) searchParams.append('page', queryParams.page.toString());
    if (queryParams.limit) searchParams.append('limit', queryParams.limit.toString());

    const queryString = searchParams.toString();
    const endpoint = `/snapshots/branch/${branchId}${queryString ? `?${queryString}` : ''}`;

    return await apiRequest(endpoint, {
      method: 'GET',
      includeAuth: true,
    });
  },

  // Get Snapshot Details
  getSnapshotDetails: async (snapshotId) => {
    return await apiRequest(`/snapshots/${snapshotId}`, {
      method: 'GET',
      includeAuth: true,
    });
  },

  // Get Flagged Reports
  getFlaggedReports: async (queryParams = {}) => {
    const searchParams = new URLSearchParams();

    // Add query parameters if provided
    if (queryParams.priority) searchParams.append('priority', queryParams.priority);
    if (queryParams.sortBy) searchParams.append('sortBy', queryParams.sortBy);
    if (queryParams.page) searchParams.append('page', queryParams.page.toString());
    if (queryParams.limit) searchParams.append('limit', queryParams.limit.toString());

    const queryString = searchParams.toString();
    const endpoint = `/snapshots/flagged${queryString ? `?${queryString}` : ''}`;

    return await apiRequest(endpoint, {
      method: 'GET',
      includeAuth: true,
    });
  },
};

// Complete authentication cleanup
export const clearAuthData = () => {
  tokenManager.removeToken();
  userManager.removeUser();
};

// Check if user is authenticated
export const isAuthenticated = () => {
  return tokenManager.hasToken() && userManager.hasUser();
};

export default {
  authAPI,
  dashboardAPI,
  tokenManager,
  userManager,
  clearAuthData,
  isAuthenticated,
};
