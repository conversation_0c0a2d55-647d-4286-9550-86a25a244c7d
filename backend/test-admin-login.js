import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api/auth';

// Admin login data
const adminLoginData = {
  email: '<EMAIL>',
  password: 'Admin123!'
};

async function testAdminLogin() {
  console.log('🧪 Testing Admin Login...');
  
  try {
    const response = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(adminLoginData)
    });
    
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Admin login successful!');
      console.log('🔑 Admin Token:', data.data.token);
      return data.data.token;
    } else {
      console.log('❌ Admin login failed!');
      return null;
    }
  } catch (error) {
    console.error('❌ Admin login error:', error.message);
    return null;
  }
}

async function testAdminProfile(token) {
  console.log('\n🧪 Testing Admin Profile...');
  
  try {
    const response = await fetch(`${BASE_URL}/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Admin profile retrieval successful!');
    } else {
      console.log('❌ Admin profile retrieval failed!');
    }
  } catch (error) {
    console.error('❌ Admin profile error:', error.message);
  }
}

async function runAdminTests() {
  console.log('🚀 Starting Vista Guard Admin Authentication Tests...');
  
  const token = await testAdminLogin();
  
  if (token) {
    await testAdminProfile(token);
  }
  
  console.log('\n🏁 Admin tests completed!');
}

runAdminTests().catch(console.error);
