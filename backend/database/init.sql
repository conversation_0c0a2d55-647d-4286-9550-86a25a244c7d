-- Create vista_guard database if it doesn't exist
CREATE DATABASE IF NOT EXISTS vista_guard;
USE vista_guard;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(191) NOT NULL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    surname VA<PERSON>HA<PERSON>(50) NOT NULL,
    email VARCHAR(191) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    companyCode VARCHAR(20) NOT NULL,
    personnelCode VARCHAR(20) NOT NULL,
    role ENUM('USER', 'ADMIN', 'SUPER_ADMIN') NOT NULL DEFAULT 'USER',
    isActive BOOLEAN NOT NULL DEFAULT TRUE,
    emailVerified BOOLEAN NOT NULL DEFAULT FALSE,
    lastLogin DATETIME NULL,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_companyCode (companyCode),
    INDEX idx_personnelCode (personnelCode)
);

-- Insert a sample admin user (password: Admin123!)
-- Password hash for 'Admin123!' with bcrypt rounds 12
INSERT IGNORE INTO users (
    id, 
    name, 
    surname, 
    email, 
    password, 
    companyCode, 
    personnelCode, 
    role, 
    isActive, 
    emailVerified
) VALUES (
    'admin-001',
    'Admin',
    'User',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5W',
    'VG001',
    'ADMIN001',
    'SUPER_ADMIN',
    TRUE,
    TRUE
);
