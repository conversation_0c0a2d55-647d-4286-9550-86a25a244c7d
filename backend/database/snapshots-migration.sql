-- Vista Guard Snapshots Migration
-- Create tables for branches, snapshots, and snapshot_reports

-- Create branches table
CREATE TABLE IF NOT EXISTS branches (
    id VARCHAR(191) NOT NULL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    city VARCHAR(255) NOT NULL,
    image VARCHAR(500) NULL,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_city (city)
);

-- Create snapshots table
CREATE TABLE IF NOT EXISTS snapshots (
    id VARCHAR(191) NOT NULL PRIMARY KEY,
    image VARCHAR(500) NOT NULL,
    type ENUM('COMPLAINT', 'NON_COMPLAINT') NOT NULL,
    branchId VARCHAR(191) NOT NULL,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_branchId (branchId),
    INDEX idx_type (type),
    INDEX idx_createdAt (createdAt),
    
    FOREIGN KEY (branchId) REFERENCES branches(id) ON DELETE CASCADE
);

-- Create snapshot_reports table
CREATE TABLE IF NOT EXISTS snapshot_reports (
    id VARCHAR(191) NOT NULL PRIMARY KEY,
    status ENUM('CHECKED', 'UNCHECKED') NOT NULL,
    details TEXT NULL,
    snapshotId VARCHAR(191) NOT NULL,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_snapshotId (snapshotId),
    INDEX idx_status (status),
    
    FOREIGN KEY (snapshotId) REFERENCES snapshots(id) ON DELETE CASCADE
);
