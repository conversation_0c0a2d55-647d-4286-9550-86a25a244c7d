import { PrismaClient } from "./src/generated/prisma/index.js";

const prisma = new PrismaClient();

async function main() {
    // Clear existing data
    await prisma.snapshotReport.deleteMany();
    await prisma.snapshot.deleteMany();
    await prisma.userAccess.deleteMany();
    await prisma.user.deleteMany();
    await prisma.branch.deleteMany();

    // Seed Branches
    await prisma.branch.createMany({
        data: [
            {
                id: 'aynzhinqt58m905r7rrlog68',
                name: 'Downtown Branch',
                city: 'New York',
                image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=500',
                createdAt: new Date('2025-07-14T20:17:53'),
                updatedAt: new Date('2025-07-14T20:17:53'),
            },
            {
                id: 'rlrl9rl5o4rajsegsmcg2d68',
                name: 'Westside Branch',
                city: 'Los Angeles',
                image: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=500',
                createdAt: new Date('2025-07-14T20:17:53'),
                updatedAt: new Date('2025-07-14T20:17:53'),
            },
        ],
    });

    // Seed Users
    await prisma.user.create({
        data: {
            id: 'admin-001',
            name: 'Admin',
            surname: 'User',
            email: '<EMAIL>',
            password: '$2b$12$k31j29u0K2L8kdUvK2GVl.NV2R8Ac0JLjahKwweLKNerhjPVC0VSy',
            companyCode: 'VG001',
            personnelCode: 'ADMIN001',
            role: 'SUPER_ADMIN',
            isActive: true,
            emailVerified: true,
            lastLogin: new Date('2025-07-14T16:46:22'),
            createdAt: new Date('2025-07-14T10:01:38'),
            updatedAt: new Date('2025-07-14T16:46:22'),
        },
    });

    // Seed Snapshots
    await prisma.snapshot.createMany({
        data: [
            {
                id: 'azspa4af1jmmfmue9y3z5o1x',
                image: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=500',
                type: 'COMPLAINT',
                branchId: 'aynzhinqt58m905r7rrlog68',
                reportPDF: 'https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf',
                createdAt: new Date('2025-07-10T20:17:53'),
                updatedAt: new Date('2025-07-14T22:06:27'),
            },
            {
                id: 'da4cxa9cce14xv9wix3egilk',
                image: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=500',
                type: 'NON_COMPLAINT',
                branchId: 'aynzhinqt58m905r7rrlog68',
                reportPDF: 'https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf',
                createdAt: new Date('2025-07-09T20:17:53'),
                updatedAt: new Date('2025-07-14T22:06:29'),
            },
            {
                id: 'f223ny5bmbw4yzga74wnh41b',
                image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=500',
                type: 'NON_COMPLAINT',
                branchId: 'aynzhinqt58m905r7rrlog68',
                reportPDF: 'https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf',
                createdAt: new Date('2025-07-11T20:17:53'),
                updatedAt: new Date('2025-07-14T22:06:30'),
            },
            {
                id: 'q4nnruwp0s1lfe8rognj8si7',
                image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=500',
                type: 'COMPLAINT',
                branchId: 'rlrl9rl5o4rajsegsmcg2d68',
                reportPDF: 'https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf',
                createdAt: new Date('2025-07-13T20:17:53'),
                updatedAt: new Date('2025-07-14T22:06:32'),
            },
            {
                id: 'uyr8csf8fl9v2r9id5ype44g',
                image: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=500',
                type: 'NON_COMPLAINT',
                branchId: 'rlrl9rl5o4rajsegsmcg2d68',
                reportPDF: 'https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf',
                createdAt: new Date('2025-07-12T20:17:53'),
                updatedAt: new Date('2025-07-14T22:06:34'),
            },
            {
                id: 'xy7fnvxg8jky99ime70mwfxe',
                image: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=500',
                type: 'NON_COMPLAINT',
                branchId: 'rlrl9rl5o4rajsegsmcg2d68',
                reportPDF: 'https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf',
                createdAt: new Date('2025-07-14T20:17:53'),
                updatedAt: new Date('2025-07-14T22:06:35'),
            },
        ],
    });

    // Seed Snapshot Reports
    await prisma.snapshotReport.createMany({
        data: [
            { id: 'g3dwn6g1kn3jygnp4aqfm9di', status: 'CHECKED', details: 'Excellent compliance with standards', snapshotId: 'xy7fnvxg8jky99ime70mwfxe', priority: 'HIGH', createdAt: new Date('2025-07-14T20:17:53'), updatedAt: new Date('2025-07-14T21:36:20') },
            { id: 'h4xj0z3ukvrgsora821tyhgf', status: 'CHECKED', details: 'Professional workspace setup', snapshotId: 'uyr8csf8fl9v2r9id5ype44g', priority: 'LOW', createdAt: new Date('2025-07-12T20:17:53'), updatedAt: new Date('2025-07-14T21:36:33') },
            { id: 'hla98hmmzt45l6jn6f0fq3am', status: 'UNCHECKED', details: 'Safety equipment not properly stored', snapshotId: 'q4nnruwp0s1lfe8rognj8si7', priority: 'HIGH', createdAt: new Date('2025-07-13T20:17:53'), updatedAt: new Date('2025-07-14T21:36:22') },
            { id: 'ijvjdn83w8nywsb6hfe5qeuz', status: 'UNCHECKED', details: 'Minor issues found during inspection', snapshotId: 'q4nnruwp0s1lfe8rognj8si7', priority: 'MEDIUM', createdAt: new Date('2025-07-13T20:17:53'), updatedAt: new Date('2025-07-14T21:36:11') },
            { id: 'jpf8tnkwasyzcxmjixgdpcy0', status: 'CHECKED', details: 'Good lighting and organization', snapshotId: 'f223ny5bmbw4yzga74wnh41b', priority: 'HIGH', createdAt: new Date('2025-07-11T20:17:53'), updatedAt: new Date('2025-07-14T21:36:25') },
            { id: 'k8yfx8ktvwune8cxdfj17wth', status: 'UNCHECKED', details: 'Messy desk area needs attention', snapshotId: 'azspa4af1jmmfmue9y3z5o1x', priority: 'LOW', createdAt: new Date('2025-07-10T20:17:53'), updatedAt: new Date('2025-07-14T21:36:36') },
            { id: 'lsnm3mneq1zblehekieurw4d', status: 'CHECKED', details: 'Clean and well-maintained office space', snapshotId: 'da4cxa9cce14xv9wix3egilk', priority: 'LOW', createdAt: new Date('2025-07-09T20:17:53'), updatedAt: new Date('2025-07-14T21:36:34') },
            { id: 'wby9sdt3wdb2hcsknb8hcucm', status: 'CHECKED', details: 'Additional compliance check passed', snapshotId: 'f223ny5bmbw4yzga74wnh41b', priority: 'MEDIUM', createdAt: new Date('2025-07-11T20:17:53'), updatedAt: new Date('2025-07-14T21:36:10') },
        ],
    });

    // Seed User Access
    await prisma.userAccess.createMany({
        data: [
            {
                id: 'KHGDIKGDIO389476',
                companyCode: 'VG001',
                personnelCode: 'ADMIN001',
                branchId: 'rlrl9rl5o4rajsegsmcg2d68',
                createdAt: new Date('2025-07-14T22:01:01'),
                updatedAt: new Date('2025-07-14T22:10:16'),
            },
            {
                id: 'KHKJGDAGDG98234',
                companyCode: 'VG001',
                personnelCode: 'ADMIN001',
                branchId: 'aynzhinqt58m905r7rrlog68',
                createdAt: new Date('2025-07-14T22:01:01'),
                updatedAt: new Date('2025-07-14T22:10:16'),
            },
        ],
    });

    console.log('✅ SQLite database seeded successfully.');
}

main()
    .catch(e => console.error(e))
    .finally(async () => await prisma.$disconnect());
