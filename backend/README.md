# Vista Guard Backend

A robust Node.js backend API for the Vista Guard web application, featuring JWT authentication, MySQL database with Prisma ORM, and comprehensive user management.

## 🚀 Features

- **JWT Authentication** - Secure token-based authentication
- **User Registration & Login** - Complete user management system
- **MySQL Database** - Reliable data storage with Prisma ORM
- **Password Security** - Bcrypt hashing with configurable rounds
- **Input Validation** - Comprehensive request validation
- **Role-based Access** - USER, ADMIN, SUPER_ADMIN roles
- **Error Handling** - Consistent error responses
- **API Documentation** - Complete endpoint documentation
- **Health Checks** - Server status monitoring

## 🛠️ Tech Stack

- **Runtime:** Node.js
- **Framework:** Express.js
- **Database:** MySQL
- **ORM:** Prisma
- **Authentication:** JWT (jsonwebtoken)
- **Password Hashing:** bcryptjs
- **Validation:** express-validator
- **Security:** helmet, cors
- **Development:** nodemon

## 📋 Prerequisites

- Node.js (v16 or higher)
- MySQL (v8.0 or higher)
- npm or yarn

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Setup
The `.env` file is already configured with default values:
```env
DATABASE_URL="mysql://root:@localhost:3306/vista_guard"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
BCRYPT_ROUNDS=12
```

### 3. Database Setup
```bash
# Initialize database and create tables
node init-db.js

# Test database connection
node test-db.js
```

### 4. Start Development Server
```bash
npm run dev
```

The server will start at `http://localhost:3000`

### 5. Test APIs
```bash
# Test authentication endpoints
node test-auth.js

# Test admin login
node test-admin-login.js
```

## 📚 API Documentation

Complete API documentation is available in [`documentation.md`](./documentation.md)

### Quick API Overview

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/health` | GET | Health check | No |
| `/api/auth/register` | POST | User registration | No |
| `/api/auth/login` | POST | User login | No |
| `/api/auth/profile` | GET | Get user profile | Yes |
| `/api/auth/logout` | POST | User logout | Yes |

## 🔐 Default Admin Account

A default admin account is created during database initialization:

- **Email:** <EMAIL>
- **Password:** Admin123!
- **Role:** SUPER_ADMIN

## 📁 Project Structure

```
backend/
├── src/
│   ├── config/
│   │   └── database.js          # Prisma client configuration
│   ├── controllers/
│   │   └── authController.js    # Authentication logic
│   ├── middleware/
│   │   └── auth.js              # JWT middleware
│   ├── routes/
│   │   ├── api.js               # Main API routes
│   │   └── auth.js              # Authentication routes
│   ├── utils/
│   │   ├── auth.js              # Auth utilities
│   │   └── validation.js        # Input validation
│   └── generated/
│       └── prisma/              # Generated Prisma client
├── database/
│   └── init.sql                 # Database initialization script
├── prisma/
│   └── schema.prisma            # Database schema
├── test-*.js                    # Test scripts
├── init-db.js                   # Database setup script
├── fix-admin-password.js        # Admin password utility
├── server.js                    # Main server file
├── package.json
├── .env                         # Environment variables
├── README.md                    # This file
└── documentation.md             # Complete API documentation
```

## 🧪 Testing

### Manual Testing Scripts

- `test-auth.js` - Test user registration, login, and profile
- `test-admin-login.js` - Test admin authentication
- `test-db.js` - Test database connection

### Running Tests
```bash
# Test all authentication endpoints
node test-auth.js

# Test admin functionality
node test-admin-login.js

# Test database connection
node test-db.js
```

## 🔧 Utility Scripts

- `init-db.js` - Initialize database and create tables
- `fix-admin-password.js` - Fix admin password if needed

## 🛡️ Security Features

- **JWT Authentication** with configurable expiration
- **Password Hashing** using bcrypt with salt rounds
- **Input Validation** for all endpoints
- **CORS Protection** with configurable origins
- **Helmet Security** headers
- **SQL Injection Protection** via Prisma ORM

## 🚀 Production Deployment

### Environment Variables for Production

1. **Change JWT_SECRET** to a strong, random secret
2. **Update DATABASE_URL** to your production MySQL instance
3. **Set NODE_ENV** to "production"
4. **Configure FRONTEND_URL** to your production frontend URL

### Recommended Production Setup

```env
NODE_ENV=production
JWT_SECRET=your-super-secure-production-secret-key
DATABASE_URL=mysql://username:password@production-host:3306/vista_guard
FRONTEND_URL=https://your-production-domain.com
BCRYPT_ROUNDS=12
```

## 📝 Development Notes

- The API uses ES6 modules (`"type": "module"` in package.json)
- Prisma client is generated to `src/generated/prisma/`
- All passwords are hashed with bcrypt (12 rounds by default)
- JWT tokens expire in 7 days by default
- Database uses CUID for user IDs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

---

**Vista Guard Backend v1.0.0**
Built with ❤️