import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const createSnapshotsTables = async () => {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'vista_guard'
    });

    console.log('Connected to MySQL database');

    // Create branches table
    const createBranchesTable = `
      CREATE TABLE IF NOT EXISTS branches (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          city VARCHAR(255) NOT NULL,
          image VARCHAR(500) NULL,
          createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          
          INDEX idx_name (name),
          INDEX idx_city (city)
      )
    `;

    await connection.execute(createBranchesTable);
    console.log('✓ Branches table created successfully');

    // Create snapshots table
    const createSnapshotsTable = `
      CREATE TABLE IF NOT EXISTS snapshots (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          image VARCHAR(500) NOT NULL,
          type ENUM('COMPLAINT', 'NON_COMPLAINT') NOT NULL,
          branchId VARCHAR(191) NOT NULL,
          createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          
          INDEX idx_branchId (branchId),
          INDEX idx_type (type),
          INDEX idx_createdAt (createdAt),
          
          FOREIGN KEY (branchId) REFERENCES branches(id) ON DELETE CASCADE
      )
    `;

    await connection.execute(createSnapshotsTable);
    console.log('✓ Snapshots table created successfully');

    // Create snapshot_reports table
    const createSnapshotReportsTable = `
      CREATE TABLE IF NOT EXISTS snapshot_reports (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          status ENUM('CHECKED', 'UNCHECKED') NOT NULL,
          details TEXT NULL,
          snapshotId VARCHAR(191) NOT NULL,
          createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          
          INDEX idx_snapshotId (snapshotId),
          INDEX idx_status (status),
          
          FOREIGN KEY (snapshotId) REFERENCES snapshots(id) ON DELETE CASCADE
      )
    `;

    await connection.execute(createSnapshotReportsTable);
    console.log('✓ Snapshot reports table created successfully');

    console.log('\n🎉 All snapshots tables created successfully!');

  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
};

createSnapshotsTables();
