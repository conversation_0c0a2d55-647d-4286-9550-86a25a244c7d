import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function testDatabaseConnection() {
  console.log('🧪 Testing MySQL Database Connection...');
  
  try {
    // Parse the DATABASE_URL
    const dbUrl = process.env.DATABASE_URL;
    console.log('Database URL:', dbUrl);
    
    // Create connection
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'vista_guard'
    });
    
    console.log('✅ Connected to MySQL database successfully!');
    
    // Test query
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ Test query successful:', rows);
    
    // Check if users table exists
    try {
      const [tables] = await connection.execute('SHOW TABLES LIKE "users"');
      if (tables.length > 0) {
        console.log('✅ Users table exists!');
        
        // Check table structure
        const [columns] = await connection.execute('DESCRIBE users');
        console.log('📋 Users table structure:');
        columns.forEach(col => {
          console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''}`);
        });
      } else {
        console.log('❌ Users table does not exist. Please run the init.sql script.');
      }
    } catch (error) {
      console.log('❌ Users table does not exist:', error.message);
    }
    
    await connection.end();
    console.log('✅ Database connection closed.');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.log('\n💡 Make sure MySQL is running and the vista_guard database exists.');
    console.log('💡 You can create the database by running the SQL commands in backend/database/init.sql');
  }
}

testDatabaseConnection();
