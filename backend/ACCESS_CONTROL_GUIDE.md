# Branch-Level Access Control Implementation Guide

## Overview

This guide explains the new branch-level access control system implemented in Vista Guard. The system ensures that users can only access branches they have explicit permission for, based on their `companyCode` and `personnelCode`.

## Database Schema

### New Table: `user_access`

```sql
CREATE TABLE user_access (
    id VARCHAR(191) PRIMARY KEY,
    companyCode VARCHAR(20) NOT NULL,
    personnelCode VARCHAR(20) NOT NULL,
    branchId VARCHAR(191) NOT NULL,
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (branchId) REFERENCES branches(id) ON DELETE CASCADE,
    UNIQUE KEY unique_access (companyCode, personnelCode, branchId),
    INDEX idx_company_personnel (companyCode, personnelCode),
    INDEX idx_branch (branchId)
);
```

## How It Works

### 1. Authentication Middleware Enhancement

The authentication middleware now:
- Fetches user's `companyCode` and `personnelCode` from the database
- Queries the `user_access` table to get all accessible branch IDs
- Attaches `accessibleBranchIds` array to the request object

### 2. API Endpoint Protection

All branch-related endpoints now implement access control:

#### `/api/branches` (GET)
- Only returns branches the user has access to
- Filters results based on `accessibleBranchIds`

#### `/api/branches/:id` (GET)
- Checks if the requested branch ID is in user's accessible branches
- Returns 403 Forbidden if access denied

#### `/api/snapshots/branch/:branchId` (GET)
- Verifies user has access to the specified branch
- Returns 403 Forbidden if access denied

#### `/api/snapshots/:snapshotId` (GET)
- Checks if user has access to the branch that owns the snapshot
- Returns 403 Forbidden if access denied

#### `/api/snapshots/flagged` (GET)
- Only returns flagged reports from accessible branches
- Filters both reports and priority statistics

## Setup Instructions

### 1. Generate Prisma Client

After adding the new schema, regenerate the Prisma client:

```bash
cd backend
npx prisma generate
```

### 2. Create User Access Records

You need to create `user_access` records for each user-branch combination. Here's how:

```javascript
// Example: Grant access to specific branches
await prisma.userAccess.create({
  data: {
    companyCode: 'COMP001',
    personnelCode: 'PERS001',
    branchId: 'branch-id-here'
  }
});
```

### 3. Bulk Access Creation

For multiple branches:

```javascript
const accessRecords = [
  { companyCode: 'COMP001', personnelCode: 'PERS001', branchId: 'branch1-id' },
  { companyCode: 'COMP001', personnelCode: 'PERS001', branchId: 'branch2-id' },
  { companyCode: 'COMP002', personnelCode: 'PERS002', branchId: 'branch2-id' },
];

await prisma.userAccess.createMany({
  data: accessRecords
});
```

## Testing

Run the included test script to verify the implementation:

```bash
cd backend
node test-access-control.js
```

This test:
- Creates test users with different company/personnel codes
- Creates test branches and access records
- Verifies that access control works correctly
- Cleans up test data

## API Response Changes

### Error Responses

When access is denied, APIs return:

```json
{
  "error": true,
  "message": "Access denied: You do not have permission to access this branch",
  "timestamp": "2025-01-14T10:30:00.000Z"
}
```

### Filtered Data

- Branch lists only include accessible branches
- Snapshot lists only include snapshots from accessible branches
- Flagged reports only include reports from accessible branches
- Statistics are calculated only from accessible data

## Security Features

### 1. Automatic Filtering
- All queries automatically filter by accessible branch IDs
- No risk of data leakage from unauthorized branches

### 2. Middleware Protection
- Access checks happen at the middleware level
- Consistent protection across all endpoints

### 3. Database-Level Constraints
- Unique constraint prevents duplicate access records
- Foreign key constraints ensure data integrity

## Management Operations

### Grant Access
```javascript
await prisma.userAccess.create({
  data: {
    companyCode: user.companyCode,
    personnelCode: user.personnelCode,
    branchId: branchId
  }
});
```

### Revoke Access
```javascript
await prisma.userAccess.delete({
  where: {
    companyCode_personnelCode_branchId: {
      companyCode: user.companyCode,
      personnelCode: user.personnelCode,
      branchId: branchId
    }
  }
});
```

### List User Access
```javascript
const userAccess = await prisma.userAccess.findMany({
  where: {
    companyCode: user.companyCode,
    personnelCode: user.personnelCode
  },
  include: {
    branch: {
      select: {
        id: true,
        name: true,
        city: true
      }
    }
  }
});
```

## Migration Notes

### Existing Users
- All existing users will have empty `accessibleBranchIds` arrays initially
- You must create `user_access` records for existing users
- Consider creating a migration script to grant appropriate access

### Backward Compatibility
- The system is backward compatible
- Users without access records will see empty results (not errors)
- This allows for gradual rollout of access controls

## Best Practices

1. **Principle of Least Privilege**: Only grant access to branches users actually need
2. **Regular Audits**: Periodically review and clean up access records
3. **Bulk Operations**: Use `createMany` for efficient bulk access grants
4. **Error Handling**: Always handle access denied scenarios gracefully in frontend
5. **Logging**: Consider adding audit logs for access grants/revocations

## Troubleshooting

### Users Can't See Any Branches
- Check if `user_access` records exist for the user
- Verify `companyCode` and `personnelCode` match exactly
- Ensure branch IDs in access records are valid

### Access Denied Errors
- Verify the user has the correct access record
- Check if the branch ID is correct
- Ensure the user is properly authenticated

### Performance Issues
- The system uses indexed queries for optimal performance
- Consider adding more specific indexes if needed
- Monitor query performance with large datasets
