import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function initializeDatabase() {
  console.log('🚀 Initializing Vista Guard Database...');

  try {
    // Create connection without database first
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: ''
    });

    console.log('✅ Connected to MySQL server successfully!');

    // Create database
    await connection.execute('CREATE DATABASE IF NOT EXISTS vista_guard');
    console.log('✅ Database vista_guard created/verified');

    // Close connection and reconnect to the specific database
    await connection.end();

    const dbConnection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'vista_guard'
    });

    console.log('✅ Connected to vista_guard database');

    // Create users table
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS users (
          id VARCHAR(191) NOT NULL PRIMARY KEY,
          name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
          surname VARCHAR(50) NOT NULL,
          email VARCHAR(191) NOT NULL UNIQUE,
          password VARCHAR(255) NOT NULL,
          companyCode VARCHAR(20) NOT NULL,
          personnelCode VARCHAR(20) NOT NULL,
          role ENUM('USER', 'ADMIN', 'SUPER_ADMIN') NOT NULL DEFAULT 'USER',
          isActive BOOLEAN NOT NULL DEFAULT TRUE,
          emailVerified BOOLEAN NOT NULL DEFAULT FALSE,
          lastLogin DATETIME NULL,
          createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

          INDEX idx_email (email),
          INDEX idx_companyCode (companyCode),
          INDEX idx_personnelCode (personnelCode)
      )
    `;

    await dbConnection.execute(createTableSQL);
    console.log('✅ Users table created/verified');

    // Insert sample admin user
    const insertAdminSQL = `
      INSERT IGNORE INTO users (
          id,
          name,
          surname,
          email,
          password,
          companyCode,
          personnelCode,
          role,
          isActive,
          emailVerified
      ) VALUES (
          'admin-001',
          'Admin',
          'User',
          '<EMAIL>',
          '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5W',
          'VG001',
          'ADMIN001',
          'SUPER_ADMIN',
          TRUE,
          TRUE
      )
    `;

    await dbConnection.execute(insertAdminSQL);
    console.log('✅ Sample admin user created/verified');

    // Verify the setup
    const [tables] = await dbConnection.execute('SHOW TABLES');
    console.log('\n📋 Created tables:');
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });

    // Check users table
    const [users] = await dbConnection.execute('SELECT COUNT(*) as count FROM users');
    console.log(`\n👥 Users in database: ${users[0].count}`);

    await dbConnection.end();
    console.log('\n✅ Database initialization completed successfully!');
    console.log('\n📝 Sample admin credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: Admin123!');

  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    process.exit(1);
  }
}

initializeDatabase();
