{"name": "vista-guard-backend", "version": "1.0.0", "description": "Backend API for Vista Guard project", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "generate": "prisma generate"}, "keywords": ["vista-guard", "api", "express", "nodejs"], "author": "", "license": "ISC", "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.11.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.2", "node-fetch": "^3.3.2"}, "devDependencies": {"nodemon": "^3.1.10", "prisma": "^6.11.1"}}