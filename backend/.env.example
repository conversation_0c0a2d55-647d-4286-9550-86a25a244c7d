# Server Configuration
PORT=3000
NODE_ENV=development

# Frontend Configuration
FRONTEND_URL=http://localhost:5173

# Database Configuration (for future use)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=vista_guard
# DB_USER=your_db_user
# DB_PASSWORD=your_db_password

# JWT Configuration (for future authentication)
# JWT_SECRET=your_super_secret_jwt_key
# JWT_EXPIRES_IN=24h

# Email Configuration (for future use)
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USER=<EMAIL>
# EMAIL_PASSWORD=your_email_password

# API Keys (for future integrations)
# API_KEY=your_api_key
