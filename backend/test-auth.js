import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api/auth';

// Test data
const testUser = {
  name: 'Test',
  surname: 'User',
  email: '<EMAIL>',
  password: 'TestPass123!',
  companyCode: 'VG001',
  personnelCode: 'TEST001'
};

const loginData = {
  email: testUser.email,
  password: testUser.password
};

async function testRegistration() {
  console.log('\n🧪 Testing User Registration...');
  
  try {
    const response = await fetch(`${BASE_URL}/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testUser)
    });
    
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Registration successful!');
      return data.data.token;
    } else {
      console.log('❌ Registration failed!');
      return null;
    }
  } catch (error) {
    console.error('❌ Registration error:', error.message);
    return null;
  }
}

async function testLogin() {
  console.log('\n🧪 Testing User Login...');
  
  try {
    const response = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(loginData)
    });
    
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Login successful!');
      return data.data.token;
    } else {
      console.log('❌ Login failed!');
      return null;
    }
  } catch (error) {
    console.error('❌ Login error:', error.message);
    return null;
  }
}

async function testProfile(token) {
  console.log('\n🧪 Testing Get Profile...');
  
  try {
    const response = await fetch(`${BASE_URL}/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Profile retrieval successful!');
    } else {
      console.log('❌ Profile retrieval failed!');
    }
  } catch (error) {
    console.error('❌ Profile error:', error.message);
  }
}

async function testHealthCheck() {
  console.log('\n🧪 Testing Health Check...');
  
  try {
    const response = await fetch('http://localhost:3000/health');
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Health check successful!');
      return true;
    } else {
      console.log('❌ Health check failed!');
      return false;
    }
  } catch (error) {
    console.error('❌ Health check error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Vista Guard Authentication API Tests...');
  
  // Test health check first
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('❌ Server is not running. Please start the server first.');
    return;
  }
  
  // Test registration
  let token = await testRegistration();
  
  // Test login
  if (!token) {
    token = await testLogin();
  }
  
  // Test profile with token
  if (token) {
    await testProfile(token);
  }
  
  console.log('\n🏁 Tests completed!');
}

// Run tests
runTests().catch(console.error);
