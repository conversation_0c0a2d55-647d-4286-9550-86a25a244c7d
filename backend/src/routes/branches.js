import express from 'express';
import { getAllBranches, getBranchById } from '../controllers/branchController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

/**
 * @route   GET /api/branches
 * @desc    Get all branches with compliance scores
 * @access  Private
 */
router.get('/', authenticateToken, getAllBranches);

/**
 * @route   GET /api/branches/:id
 * @desc    Get a specific branch by ID with compliance score
 * @access  Private
 */
router.get('/:id', authenticateToken, getBranchById);

export default router;
