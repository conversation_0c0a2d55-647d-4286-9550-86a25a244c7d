import express from 'express';
import { register, login, getProfile } from '../controllers/authController.js';
import { registerValidation, loginValidation, handleValidationErrors } from '../utils/validation.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

/**
 * @route   POST /api/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', 
  registerValidation,
  handleValidationErrors,
  register
);

/**
 * @route   POST /api/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login',
  loginValidation,
  handleValidationErrors,
  login
);

/**
 * @route   GET /api/auth/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile',
  authenticateToken,
  getProfile
);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user (client-side token removal)
 * @access  Private
 */
router.post('/logout',
  authenticateToken,
  (req, res) => {
    res.status(200).json({
      success: true,
      message: 'Logout successful. Please remove the token from client storage.',
      timestamp: new Date().toISOString()
    });
  }
);

export default router;
