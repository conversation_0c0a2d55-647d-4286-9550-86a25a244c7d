import express from 'express';
import { getBranchSnapshots, getSnapshotDetails, getFlaggedReports } from '../controllers/snapshotController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

/**
 * @route   GET /api/snapshots/branch/:branchId
 * @desc    Get snapshots for a specific branch with stats and filtering
 * @access  Private
 * @query   type (optional) - Filter by COMPLAINT or NON_COMPLAINT
 * @query   sortBy (optional) - Sort by 'newest' or 'oldest' (default: newest)
 * @query   page (optional) - Page number for pagination (default: 1)
 * @query   limit (optional) - Number of items per page (default: 10)
 */
router.get('/branch/:branchId', authenticateToken, getBranchSnapshots);

/**
 * @route   GET /api/snapshots/flagged
 * @desc    Get flagged reports (UNCHECKED status) with pagination and priority filtering
 * @access  Private
 * @query   priority (optional) - Filter by HIGH, MEDIUM, or LOW priority
 * @query   sortBy (optional) - Sort by 'newest', 'oldest', or 'priority' (default: newest)
 * @query   page (optional) - Page number for pagination (default: 1)
 * @query   limit (optional) - Number of items per page (default: 10)
 */
router.get('/flagged', authenticateToken, getFlaggedReports);

/**
 * @route   GET /api/snapshots/:snapshotId
 * @desc    Get snapshot details by ID with all related reports
 * @access  Private
 */
router.get('/:snapshotId', authenticateToken, getSnapshotDetails);

export default router;
