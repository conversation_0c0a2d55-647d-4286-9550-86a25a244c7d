import prisma from '../config/database.js';

/**
 * Check if user has access to a specific branch
 * @param {string} companyCode - User's company code
 * @param {string} personnelCode - User's personnel code
 * @param {string} branchId - Branch ID to check access for
 * @returns {Promise<boolean>} - True if user has access
 */
export const hasAccessToBranch = async (companyCode, personnelCode, branchId) => {
  try {
    const access = await prisma.userAccess.findFirst({
      where: {
        companyCode,
        personnelCode,
        branchId
      }
    });
    
    return !!access;
  } catch (error) {
    console.error('Error checking branch access:', error);
    return false;
  }
};

/**
 * Get all branch IDs that a user has access to
 * @param {string} companyCode - User's company code
 * @param {string} personnelCode - User's personnel code
 * @returns {Promise<string[]>} - Array of accessible branch IDs
 */
export const getAccessibleBranchIds = async (companyCode, personnelCode) => {
  try {
    const userAccess = await prisma.userAccess.findMany({
      where: {
        companyCode,
        personnelCode
      },
      select: {
        branchId: true
      }
    });
    
    return userAccess.map(access => access.branchId);
  } catch (error) {
    console.error('Error fetching accessible branches:', error);
    return [];
  }
};

/**
 * Filter branches based on user access
 * @param {Array} branches - Array of branch objects
 * @param {string[]} accessibleBranchIds - Array of accessible branch IDs
 * @returns {Array} - Filtered array of branches
 */
export const filterAccessibleBranches = (branches, accessibleBranchIds) => {
  return branches.filter(branch => accessibleBranchIds.includes(branch.id));
};

/**
 * Check if user has access to a branch by snapshot ID
 * @param {string} companyCode - User's company code
 * @param {string} personnelCode - User's personnel code
 * @param {string} snapshotId - Snapshot ID to check access for
 * @returns {Promise<boolean>} - True if user has access to the branch that owns the snapshot
 */
export const hasAccessToSnapshotBranch = async (companyCode, personnelCode, snapshotId) => {
  try {
    const snapshot = await prisma.snapshot.findUnique({
      where: { id: snapshotId },
      select: { branchId: true }
    });
    
    if (!snapshot) {
      return false;
    }
    
    return await hasAccessToBranch(companyCode, personnelCode, snapshot.branchId);
  } catch (error) {
    console.error('Error checking snapshot branch access:', error);
    return false;
  }
};

/**
 * Middleware to check branch access
 * @param {string} branchIdParam - Name of the parameter containing branch ID (default: 'id')
 * @returns {Function} - Express middleware function
 */
export const requireBranchAccess = (branchIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      const branchId = req.params[branchIdParam];
      const { companyCode, personnelCode } = req.user;
      
      if (!branchId) {
        return res.status(400).json({
          error: true,
          message: 'Branch ID is required',
          timestamp: new Date().toISOString()
        });
      }
      
      const hasAccess = await hasAccessToBranch(companyCode, personnelCode, branchId);
      
      if (!hasAccess) {
        return res.status(403).json({
          error: true,
          message: 'Access denied: You do not have permission to access this branch',
          timestamp: new Date().toISOString()
        });
      }
      
      next();
    } catch (error) {
      console.error('Error in branch access middleware:', error);
      res.status(500).json({
        error: true,
        message: 'Internal server error during access check',
        timestamp: new Date().toISOString()
      });
    }
  };
};

/**
 * Middleware to check snapshot branch access
 * @param {string} snapshotIdParam - Name of the parameter containing snapshot ID (default: 'snapshotId')
 * @returns {Function} - Express middleware function
 */
export const requireSnapshotBranchAccess = (snapshotIdParam = 'snapshotId') => {
  return async (req, res, next) => {
    try {
      const snapshotId = req.params[snapshotIdParam];
      const { companyCode, personnelCode } = req.user;
      
      if (!snapshotId) {
        return res.status(400).json({
          error: true,
          message: 'Snapshot ID is required',
          timestamp: new Date().toISOString()
        });
      }
      
      const hasAccess = await hasAccessToSnapshotBranch(companyCode, personnelCode, snapshotId);
      
      if (!hasAccess) {
        return res.status(403).json({
          error: true,
          message: 'Access denied: You do not have permission to access this snapshot',
          timestamp: new Date().toISOString()
        });
      }
      
      next();
    } catch (error) {
      console.error('Error in snapshot branch access middleware:', error);
      res.status(500).json({
        error: true,
        message: 'Internal server error during access check',
        timestamp: new Date().toISOString()
      });
    }
  };
};
