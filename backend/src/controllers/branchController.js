import prisma from '../config/database.js';
import { filterAccessibleBranches } from '../utils/accessControl.js';

/**
 * Get all branches with compliance scores
 */
export const getAllBranches = async (req, res) => {
  try {
    const { accessibleBranchIds } = req.user;

    // Fetch only branches that the user has access to
    const branches = await prisma.branch.findMany({
      where: {
        id: {
          in: accessibleBranchIds
        }
      },
      include: {
        snapshots: {
          include: {
            reports: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Calculate compliance scores for each branch
    const branchesWithCompliance = branches.map(branch => {
      let totalReports = 0;
      let checkedReports = 0;

      // Count all reports across all snapshots for this branch
      branch.snapshots.forEach(snapshot => {
        snapshot.reports.forEach(report => {
          totalReports++;
          if (report.status === 'CHECKED') {
            checkedReports++;
          }
        });
      });

      // Calculate compliance percentage
      const complianceScore = totalReports > 0 
        ? Math.round((checkedReports / totalReports) * 100) 
        : 0;

      // Return branch data without nested snapshots/reports
      return {
        id: branch.id,
        name: branch.name,
        city: branch.city,
        image: branch.image,
        createdAt: branch.createdAt,
        updatedAt: branch.updatedAt,
        complianceScore,
        stats: {
          totalSnapshots: branch.snapshots.length,
          totalReports: totalReports,
          checkedReports: checkedReports,
          uncheckedReports: totalReports - checkedReports
        }
      };
    });

    res.status(200).json({
      success: true,
      message: 'Branches retrieved successfully',
      data: {
        branches: branchesWithCompliance,
        total: branchesWithCompliance.length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching branches:', error);
    res.status(500).json({
      error: true,
      message: 'Internal server error while fetching branches',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get a specific branch by ID with compliance score
 */
export const getBranchById = async (req, res) => {
  try {
    const { id } = req.params;
    const { accessibleBranchIds } = req.user;

    // Check if user has access to this branch
    if (!accessibleBranchIds.includes(id)) {
      return res.status(403).json({
        error: true,
        message: 'Access denied: You do not have permission to access this branch',
        timestamp: new Date().toISOString()
      });
    }

    const branch = await prisma.branch.findUnique({
      where: { id },
      include: {
        snapshots: {
          include: {
            reports: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    if (!branch) {
      return res.status(404).json({
        error: true,
        message: 'Branch not found',
        timestamp: new Date().toISOString()
      });
    }

    // Calculate compliance score
    let totalReports = 0;
    let checkedReports = 0;

    branch.snapshots.forEach(snapshot => {
      snapshot.reports.forEach(report => {
        totalReports++;
        if (report.status === 'CHECKED') {
          checkedReports++;
        }
      });
    });

    const complianceScore = totalReports > 0 
      ? Math.round((checkedReports / totalReports) * 100) 
      : 0;

    // Return branch data without nested snapshots/reports
    const branchData = {
      id: branch.id,
      name: branch.name,
      city: branch.city,
      image: branch.image,
      createdAt: branch.createdAt,
      updatedAt: branch.updatedAt,
      complianceScore,
      stats: {
        totalSnapshots: branch.snapshots.length,
        totalReports: totalReports,
        checkedReports: checkedReports,
        uncheckedReports: totalReports - checkedReports
      }
    };

    res.status(200).json({
      success: true,
      message: 'Branch retrieved successfully',
      data: {
        branch: branchData
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching branch:', error);
    res.status(500).json({
      error: true,
      message: 'Internal server error while fetching branch',
      timestamp: new Date().toISOString()
    });
  }
};
