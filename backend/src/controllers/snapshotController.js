import prisma from '../config/database.js';

/**
 * Get snapshots for a specific branch with stats and filtering
 */
export const getBranchSnapshots = async (req, res) => {
  try {
    const { branchId } = req.params;
    const {
      type,
      sortBy = 'newest',
      page = 1,
      limit = 10
    } = req.query;
    const { accessibleBranchIds } = req.user;

    // Check if user has access to this branch
    if (!accessibleBranchIds.includes(branchId)) {
      return res.status(403).json({
        error: true,
        message: 'Access denied: You do not have permission to access this branch',
        timestamp: new Date().toISOString()
      });
    }

    // Validate branch exists
    const branch = await prisma.branch.findUnique({
      where: { id: branchId }
    });

    if (!branch) {
      return res.status(404).json({
        error: true,
        message: 'Branch not found',
        timestamp: new Date().toISOString()
      });
    }

    // Build where clause for filtering
    const whereClause = {
      branchId: branchId
    };

    if (type && ['COMPLAINT', 'NON_COMPLAINT'].includes(type.toUpperCase())) {
      whereClause.type = type.toUpperCase();
    }

    // Build orderBy clause for sorting
    let orderBy = {};
    switch (sortBy.toLowerCase()) {
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'newest':
      default:
        orderBy = { createdAt: 'desc' };
        break;
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Fetch snapshots with pagination
    const [snapshots, totalCount] = await Promise.all([
      prisma.snapshot.findMany({
        where: whereClause,
        include: {
          reports: true,
          branch: {
            select: {
              id: true,
              name: true,
              city: true
            }
          }
        },
        orderBy,
        skip,
        take
      }),
      prisma.snapshot.count({
        where: whereClause
      })
    ]);

    // Get overall stats for the branch
    const [
      totalSnapshots,
      complaintSnapshots,
      nonComplaintSnapshots,
      allReports
    ] = await Promise.all([
      prisma.snapshot.count({
        where: { branchId }
      }),
      prisma.snapshot.count({
        where: { branchId, type: 'COMPLAINT' }
      }),
      prisma.snapshot.count({
        where: { branchId, type: 'NON_COMPLAINT' }
      }),
      prisma.snapshotReport.findMany({
        where: {
          snapshot: {
            branchId
          }
        }
      })
    ]);

    // Calculate compliance rate
    const totalReports = allReports.length;
    const checkedReports = allReports.filter(report => report.status === 'CHECKED').length;
    const complianceRate = totalReports > 0 
      ? Math.round((checkedReports / totalReports) * 100) 
      : 0;

    // Format snapshots data
    const formattedSnapshots = snapshots.map(snapshot => {
      const totalSnapshotReports = snapshot.reports.length;
      const checkedSnapshotReports = snapshot.reports.filter(report => report.status === 'CHECKED').length;
      const snapshotComplianceRate = totalSnapshotReports > 0 
        ? Math.round((checkedSnapshotReports / totalSnapshotReports) * 100) 
        : 0;

      return {
        id: snapshot.id,
        image: snapshot.image,
        type: snapshot.type,
        reportPDF: snapshot.reportPDF,
        createdAt: snapshot.createdAt,
        updatedAt: snapshot.updatedAt,
        branch: snapshot.branch,
        complianceRate: snapshotComplianceRate,
        reportsCount: {
          total: totalSnapshotReports,
          checked: checkedSnapshotReports,
          unchecked: totalSnapshotReports - checkedSnapshotReports
        }
      };
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / take);
    const hasNextPage = parseInt(page) < totalPages;
    const hasPrevPage = parseInt(page) > 1;

    res.status(200).json({
      success: true,
      message: 'Branch snapshots retrieved successfully',
      data: {
        snapshots: formattedSnapshots,
        stats: {
          totalSnapshots,
          complaintSnapshots,
          nonComplaintSnapshots,
          overallComplianceRate: complianceRate,
          totalReports,
          checkedReports,
          uncheckedReports: totalReports - checkedReports
        },
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          hasNextPage,
          hasPrevPage,
          limit: take
        },
        filters: {
          type: type || 'all',
          sortBy
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching branch snapshots:', error);
    res.status(500).json({
      error: true,
      message: 'Internal server error while fetching snapshots',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get snapshot details by ID with all related reports
 */
export const getSnapshotDetails = async (req, res) => {
  try {
    const { snapshotId } = req.params;
    const { accessibleBranchIds } = req.user;

    const snapshot = await prisma.snapshot.findUnique({
      where: { id: snapshotId },
      include: {
        reports: {
          orderBy: {
            createdAt: 'desc'
          }
        },
        branch: {
          select: {
            id: true,
            name: true,
            city: true,
            image: true
          }
        }
      }
    });

    if (!snapshot) {
      return res.status(404).json({
        error: true,
        message: 'Snapshot not found',
        timestamp: new Date().toISOString()
      });
    }

    // Check if user has access to the branch that owns this snapshot
    if (!accessibleBranchIds.includes(snapshot.branchId)) {
      return res.status(403).json({
        error: true,
        message: 'Access denied: You do not have permission to access this snapshot',
        timestamp: new Date().toISOString()
      });
    }

    // Calculate compliance stats for this snapshot
    const totalReports = snapshot.reports.length;
    const checkedReports = snapshot.reports.filter(report => report.status === 'CHECKED').length;
    const uncheckedReports = totalReports - checkedReports;
    const complianceRate = totalReports > 0
      ? Math.round((checkedReports / totalReports) * 100)
      : 0;

    // Format the response
    const snapshotDetails = {
      id: snapshot.id,
      image: snapshot.image,
      type: snapshot.type,
      reportPDF: snapshot.reportPDF,
      createdAt: snapshot.createdAt,
      updatedAt: snapshot.updatedAt,
      branch: snapshot.branch,
      complianceRate,
      stats: {
        totalReports,
        checkedReports,
        uncheckedReports
      },
      reports: snapshot.reports.map(report => ({
        id: report.id,
        status: report.status,
        details: report.details,
        createdAt: report.createdAt,
        updatedAt: report.updatedAt
      }))
    };

    res.status(200).json({
      success: true,
      message: 'Snapshot details retrieved successfully',
      data: {
        snapshot: snapshotDetails
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching snapshot details:', error);
    res.status(500).json({
      error: true,
      message: 'Internal server error while fetching snapshot details',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get flagged reports (UNCHECKED status) with pagination and priority filtering
 */
export const getFlaggedReports = async (req, res) => {
  try {
    const {
      priority,
      sortBy = 'newest',
      page = 1,
      limit = 10
    } = req.query;
    const { accessibleBranchIds } = req.user;

    // Convert page and limit to integers
    const take = Math.min(parseInt(limit), 50); // Max 50 items per page
    const skip = (parseInt(page) - 1) * take;

    // Build where clause for filtering flagged reports
    const whereClause = {
      status: 'UNCHECKED', // Only flagged (unchecked) reports
      snapshot: {
        branchId: {
          in: accessibleBranchIds // Only reports from accessible branches
        }
      }
    };

    // Add priority filter if specified
    if (priority && ['HIGH', 'MEDIUM', 'LOW'].includes(priority.toUpperCase())) {
      whereClause.priority = priority.toUpperCase();
    }

    // Build orderBy clause
    let orderBy = {};
    switch (sortBy) {
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'priority':
        orderBy = [
          { priority: 'asc' }, // HIGH first (alphabetically), then LOW, then MEDIUM
          { createdAt: 'desc' }
        ];
        break;
      case 'newest':
      default:
        orderBy = { createdAt: 'desc' };
        break;
    }

    // Get flagged reports with related data
    const [flaggedReports, totalCount] = await Promise.all([
      prisma.snapshotReport.findMany({
        where: whereClause,
        include: {
          snapshot: {
            include: {
              branch: true
            }
          }
        },
        orderBy,
        take,
        skip
      }),
      prisma.snapshotReport.count({
        where: whereClause
      })
    ]);

    // Format the response data
    const formattedReports = flaggedReports.map(report => ({
      id: report.id,
      status: report.status,
      priority: report.priority,
      details: report.details,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt,
      snapshot: {
        id: report.snapshot.id,
        image: report.snapshot.image,
        type: report.snapshot.type,
        reportPDF: report.snapshot.reportPDF,
        createdAt: report.snapshot.createdAt,
        branch: {
          id: report.snapshot.branch.id,
          name: report.snapshot.branch.name,
          city: report.snapshot.branch.city,
          image: report.snapshot.branch.image
        }
      }
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / take);
    const hasNextPage = parseInt(page) < totalPages;
    const hasPrevPage = parseInt(page) > 1;

    // Get priority stats for the response (only from accessible branches)
    const priorityStats = await prisma.snapshotReport.groupBy({
      by: ['priority'],
      where: {
        status: 'UNCHECKED',
        snapshot: {
          branchId: {
            in: accessibleBranchIds
          }
        }
      },
      _count: {
        priority: true
      }
    });

    const stats = {
      total: totalCount,
      high: priorityStats.find(stat => stat.priority === 'HIGH')?._count?.priority || 0,
      medium: priorityStats.find(stat => stat.priority === 'MEDIUM')?._count?.priority || 0,
      low: priorityStats.find(stat => stat.priority === 'LOW')?._count?.priority || 0
    };

    res.status(200).json({
      success: true,
      message: 'Flagged reports retrieved successfully',
      data: {
        reports: formattedReports,
        stats,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          hasNextPage,
          hasPrevPage,
          limit: take
        },
        filters: {
          priority: priority || 'all',
          sortBy
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching flagged reports:', error);
    res.status(500).json({
      error: true,
      message: 'Failed to fetch flagged reports',
      timestamp: new Date().toISOString()
    });
  }
};
