import { verifyToken, extractToken } from '../utils/auth.js';
import prisma from '../config/database.js';

/**
 * Middleware to authenticate JWT tokens
 */
export const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractToken(authHeader);

    if (!token) {
      return res.status(401).json({
        error: true,
        message: 'Access token is required',
        timestamp: new Date().toISOString()
      });
    }

    // Verify the token
    const decoded = verifyToken(token);
    
    // Check if user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        role: true,
        isActive: true,
        companyCode: true,
        personnelCode: true
      }
    });

    if (!user) {
      return res.status(401).json({
        error: true,
        message: 'User not found',
        timestamp: new Date().toISOString()
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        error: true,
        message: 'Account is deactivated',
        timestamp: new Date().toISOString()
      });
    }

    // Fetch user's accessible branches
    const userAccess = await prisma.userAccess.findMany({
      where: {
        companyCode: user.companyCode,
        personnelCode: user.personnelCode
      },
      select: {
        branchId: true
      }
    });

    // Attach user info and accessible branch IDs to request object
    req.user = {
      ...user,
      accessibleBranchIds: userAccess.map(access => access.branchId)
    };
    next();

  } catch (error) {
    console.error('Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: true,
        message: 'Invalid token',
        timestamp: new Date().toISOString()
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: true,
        message: 'Token has expired',
        timestamp: new Date().toISOString()
      });
    }

    res.status(500).json({
      error: true,
      message: 'Internal server error during authentication',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Middleware to check if user has required role
 */
export const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: true,
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      });
    }

    const userRole = req.user.role;
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        error: true,
        message: 'Insufficient permissions',
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};

/**
 * Middleware to check if user is admin
 */
export const requireAdmin = requireRole(['ADMIN', 'SUPER_ADMIN']);

/**
 * Middleware to check if user is super admin
 */
export const requireSuperAdmin = requireRole(['SUPER_ADMIN']);
