import mysql from 'mysql2/promise';
import { createId } from '@paralleldrive/cuid2';
import dotenv from 'dotenv';

dotenv.config();

const seedSnapshotsData = async () => {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'vista_guard'
    });

    console.log('Connected to MySQL database');

    // Generate branch IDs
    const branch1Id = createId();
    const branch2Id = createId();

    // Insert branches
    const insertBranches = `
      INSERT INTO branches (id, name, city, image, createdAt, updatedAt) VALUES
      (?, 'Downtown Branch', 'New York', 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=500', NOW(), NOW()),
      (?, 'Westside Branch', 'Los Angeles', 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=500', NOW(), NOW())
    `;

    await connection.execute(insertBranches, [branch1Id, branch2Id]);
    console.log('✓ Branches seeded successfully');

    // Generate snapshot IDs
    const snapshot1Id = createId();
    const snapshot2Id = createId();
    const snapshot3Id = createId();
    const snapshot4Id = createId();
    const snapshot5Id = createId();
    const snapshot6Id = createId();

    // Insert snapshots
    const insertSnapshots = `
      INSERT INTO snapshots (id, image, type, branchId, createdAt, updatedAt) VALUES
      (?, 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=500', 'NON_COMPLAINT', ?, NOW() - INTERVAL 5 DAY, NOW() - INTERVAL 5 DAY),
      (?, 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=500', 'COMPLAINT', ?, NOW() - INTERVAL 4 DAY, NOW() - INTERVAL 4 DAY),
      (?, 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=500', 'NON_COMPLAINT', ?, NOW() - INTERVAL 3 DAY, NOW() - INTERVAL 3 DAY),
      (?, 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=500', 'NON_COMPLAINT', ?, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY),
      (?, 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=500', 'COMPLAINT', ?, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
      (?, 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=500', 'NON_COMPLAINT', ?, NOW(), NOW())
    `;

    await connection.execute(insertSnapshots, [
      snapshot1Id, branch1Id,
      snapshot2Id, branch1Id,
      snapshot3Id, branch1Id,
      snapshot4Id, branch2Id,
      snapshot5Id, branch2Id,
      snapshot6Id, branch2Id
    ]);
    console.log('✓ Snapshots seeded successfully');

    // Generate snapshot report IDs
    const report1Id = createId();
    const report2Id = createId();
    const report3Id = createId();
    const report4Id = createId();
    const report5Id = createId();
    const report6Id = createId();
    const report7Id = createId();
    const report8Id = createId();

    // Insert snapshot reports
    const insertReports = `
      INSERT INTO snapshot_reports (id, status, details, snapshotId, createdAt, updatedAt) VALUES
      (?, 'CHECKED', 'Clean and well-maintained office space', ?, NOW() - INTERVAL 5 DAY, NOW() - INTERVAL 5 DAY),
      (?, 'UNCHECKED', 'Messy desk area needs attention', ?, NOW() - INTERVAL 4 DAY, NOW() - INTERVAL 4 DAY),
      (?, 'CHECKED', 'Good lighting and organization', ?, NOW() - INTERVAL 3 DAY, NOW() - INTERVAL 3 DAY),
      (?, 'CHECKED', 'Professional workspace setup', ?, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY),
      (?, 'UNCHECKED', 'Safety equipment not properly stored', ?, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
      (?, 'CHECKED', 'Excellent compliance with standards', ?, NOW(), NOW()),
      (?, 'CHECKED', 'Additional compliance check passed', ?, NOW() - INTERVAL 3 DAY, NOW() - INTERVAL 3 DAY),
      (?, 'UNCHECKED', 'Minor issues found during inspection', ?, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY)
    `;

    await connection.execute(insertReports, [
      report1Id, snapshot1Id,
      report2Id, snapshot2Id,
      report3Id, snapshot3Id,
      report4Id, snapshot4Id,
      report5Id, snapshot5Id,
      report6Id, snapshot6Id,
      report7Id, snapshot3Id, // Additional report for snapshot3
      report8Id, snapshot5Id  // Additional report for snapshot5
    ]);
    console.log('✓ Snapshot reports seeded successfully');

    console.log('\n🎉 All seed data inserted successfully!');
    console.log('\nSeed Summary:');
    console.log('- 2 Branches created');
    console.log('- 6 Snapshots created (3 per branch)');
    console.log('- 8 Snapshot reports created');

  } catch (error) {
    console.error('❌ Error seeding data:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
};

seedSnapshotsData();
