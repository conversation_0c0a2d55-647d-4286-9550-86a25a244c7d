import prisma from './src/config/database.js';
import { hashPassword } from './src/utils/auth.js';

/**
 * Test script to verify access control implementation
 * This script creates test data and verifies that access control works correctly
 */

async function createTestData() {
  console.log('Creating test data for access control...');

  try {
    // Create test users
    const hashedPassword = await hashPassword('testpassword123');
    
    const user1 = await prisma.user.create({
      data: {
        name: '<PERSON>',
        surname: '<PERSON><PERSON>',
        email: '<EMAIL>',
        password: hashedPassword,
        companyCode: 'COMP001',
        personnelCode: 'PERS001'
      }
    });

    const user2 = await prisma.user.create({
      data: {
        name: '<PERSON>',
        surname: '<PERSON>',
        email: '<EMAIL>',
        password: hashedPassword,
        companyCode: 'COMP002',
        personnelCode: 'PERS002'
      }
    });

    // Create test branches
    const branch1 = await prisma.branch.create({
      data: {
        name: 'Branch Alpha',
        city: 'New York',
        image: 'branch1.jpg'
      }
    });

    const branch2 = await prisma.branch.create({
      data: {
        name: 'Branch Beta',
        city: 'Los Angeles',
        image: 'branch2.jpg'
      }
    });

    const branch3 = await prisma.branch.create({
      data: {
        name: 'Branch Gamma',
        city: 'Chicago',
        image: 'branch3.jpg'
      }
    });

    // Create user access records
    // User1 has access to Branch1 and Branch2
    await prisma.userAccess.create({
      data: {
        companyCode: 'COMP001',
        personnelCode: 'PERS001',
        branchId: branch1.id
      }
    });

    await prisma.userAccess.create({
      data: {
        companyCode: 'COMP001',
        personnelCode: 'PERS001',
        branchId: branch2.id
      }
    });

    // User2 has access to Branch2 and Branch3
    await prisma.userAccess.create({
      data: {
        companyCode: 'COMP002',
        personnelCode: 'PERS002',
        branchId: branch2.id
      }
    });

    await prisma.userAccess.create({
      data: {
        companyCode: 'COMP002',
        personnelCode: 'PERS002',
        branchId: branch3.id
      }
    });

    // Create test snapshots
    const snapshot1 = await prisma.snapshot.create({
      data: {
        image: 'snapshot1.jpg',
        type: 'COMPLAINT',
        branchId: branch1.id
      }
    });

    const snapshot2 = await prisma.snapshot.create({
      data: {
        image: 'snapshot2.jpg',
        type: 'NON_COMPLAINT',
        branchId: branch2.id
      }
    });

    const snapshot3 = await prisma.snapshot.create({
      data: {
        image: 'snapshot3.jpg',
        type: 'COMPLAINT',
        branchId: branch3.id
      }
    });

    // Create test reports
    await prisma.snapshotReport.create({
      data: {
        status: 'UNCHECKED',
        details: 'Test report 1',
        priority: 'HIGH',
        snapshotId: snapshot1.id
      }
    });

    await prisma.snapshotReport.create({
      data: {
        status: 'CHECKED',
        details: 'Test report 2',
        priority: 'MEDIUM',
        snapshotId: snapshot2.id
      }
    });

    await prisma.snapshotReport.create({
      data: {
        status: 'UNCHECKED',
        details: 'Test report 3',
        priority: 'LOW',
        snapshotId: snapshot3.id
      }
    });

    console.log('✅ Test data created successfully!');
    console.log(`User1 (${user1.email}) has access to: Branch Alpha, Branch Beta`);
    console.log(`User2 (${user2.email}) has access to: Branch Beta, Branch Gamma`);
    
    return {
      users: { user1, user2 },
      branches: { branch1, branch2, branch3 },
      snapshots: { snapshot1, snapshot2, snapshot3 }
    };

  } catch (error) {
    console.error('❌ Error creating test data:', error);
    throw error;
  }
}

async function testAccessControl() {
  console.log('\n🧪 Testing access control implementation...');

  try {
    // Test 1: Check user access records
    console.log('\n📋 Test 1: Verifying user access records');
    
    const user1Access = await prisma.userAccess.findMany({
      where: {
        companyCode: 'COMP001',
        personnelCode: 'PERS001'
      },
      include: {
        branch: {
          select: {
            name: true
          }
        }
      }
    });

    const user2Access = await prisma.userAccess.findMany({
      where: {
        companyCode: 'COMP002',
        personnelCode: 'PERS002'
      },
      include: {
        branch: {
          select: {
            name: true
          }
        }
      }
    });

    console.log(`User1 accessible branches: ${user1Access.map(a => a.branch.name).join(', ')}`);
    console.log(`User2 accessible branches: ${user2Access.map(a => a.branch.name).join(', ')}`);

    // Test 2: Verify branch filtering works
    console.log('\n📋 Test 2: Testing branch filtering');
    
    const user1BranchIds = user1Access.map(a => a.branchId);
    const user2BranchIds = user2Access.map(a => a.branchId);

    const user1Branches = await prisma.branch.findMany({
      where: {
        id: {
          in: user1BranchIds
        }
      }
    });

    const user2Branches = await prisma.branch.findMany({
      where: {
        id: {
          in: user2BranchIds
        }
      }
    });

    console.log(`User1 can access ${user1Branches.length} branches: ${user1Branches.map(b => b.name).join(', ')}`);
    console.log(`User2 can access ${user2Branches.length} branches: ${user2Branches.map(b => b.name).join(', ')}`);

    // Test 3: Verify flagged reports filtering
    console.log('\n📋 Test 3: Testing flagged reports filtering');
    
    const user1FlaggedReports = await prisma.snapshotReport.findMany({
      where: {
        status: 'UNCHECKED',
        snapshot: {
          branchId: {
            in: user1BranchIds
          }
        }
      },
      include: {
        snapshot: {
          include: {
            branch: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    const user2FlaggedReports = await prisma.snapshotReport.findMany({
      where: {
        status: 'UNCHECKED',
        snapshot: {
          branchId: {
            in: user2BranchIds
          }
        }
      },
      include: {
        snapshot: {
          include: {
            branch: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    console.log(`User1 can see ${user1FlaggedReports.length} flagged reports from accessible branches`);
    console.log(`User2 can see ${user2FlaggedReports.length} flagged reports from accessible branches`);

    console.log('\n✅ Access control tests completed successfully!');
    console.log('\n📝 Summary:');
    console.log('- User access records are properly created');
    console.log('- Branch filtering works correctly');
    console.log('- Flagged reports are filtered by accessible branches');
    console.log('- Each user can only see data from branches they have access to');

  } catch (error) {
    console.error('❌ Error during access control testing:', error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    // Delete in reverse order of dependencies
    await prisma.snapshotReport.deleteMany({
      where: {
        details: {
          startsWith: 'Test report'
        }
      }
    });

    await prisma.snapshot.deleteMany({
      where: {
        image: {
          in: ['snapshot1.jpg', 'snapshot2.jpg', 'snapshot3.jpg']
        }
      }
    });

    await prisma.userAccess.deleteMany({
      where: {
        companyCode: {
          in: ['COMP001', 'COMP002']
        }
      }
    });

    await prisma.branch.deleteMany({
      where: {
        name: {
          in: ['Branch Alpha', 'Branch Beta', 'Branch Gamma']
        }
      }
    });

    await prisma.user.deleteMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>']
        }
      }
    });

    console.log('✅ Test data cleaned up successfully!');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
    throw error;
  }
}

async function main() {
  try {
    // Clean up any existing test data first
    await cleanupTestData();
    await createTestData();
    await testAccessControl();
    await cleanupTestData();
    
    console.log('\n🎉 All access control tests passed!');
    console.log('\n📋 Next steps:');
    console.log('1. Create user_access records for your actual users');
    console.log('2. Test the API endpoints with authenticated users');
    console.log('3. Verify that users can only access their permitted branches');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
