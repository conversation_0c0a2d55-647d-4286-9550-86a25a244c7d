import bcrypt from 'bcryptjs';
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function fixAdminPassword() {
  console.log('🔧 Fixing Admin Password...');
  
  try {
    // Generate correct password hash
    const password = 'Admin123!';
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    console.log('✅ Generated password hash:', hashedPassword);
    
    // Connect to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'vista_guard'
    });
    
    console.log('✅ Connected to database');
    
    // Update admin password
    const [result] = await connection.execute(
      'UPDATE users SET password = ? WHERE email = ?',
      [hashedPassword, '<EMAIL>']
    );
    
    console.log('✅ Updated admin password. Affected rows:', result.affectedRows);
    
    // Verify the update
    const [users] = await connection.execute(
      'SELECT id, name, surname, email, role FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    if (users.length > 0) {
      console.log('✅ Admin user found:', users[0]);
    } else {
      console.log('❌ Admin user not found');
    }
    
    await connection.end();
    console.log('✅ Database connection closed');
    
    // Test the password
    const isValid = await bcrypt.compare(password, hashedPassword);
    console.log('✅ Password verification test:', isValid ? 'PASSED' : 'FAILED');
    
  } catch (error) {
    console.error('❌ Error fixing admin password:', error.message);
  }
}

fixAdminPassword();
