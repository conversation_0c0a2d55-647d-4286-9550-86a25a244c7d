import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

// Test credentials (using the admin user from seed data)
const testCredentials = {
  email: '<EMAIL>',
  password: 'Admin123!'
};

let authToken = '';

const testAPI = async () => {
  try {
    console.log('🚀 Testing Vista Guard Snapshots API\n');

    // Step 1: Login to get auth token
    console.log('1. Testing login...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testCredentials)
    });

    const loginData = await loginResponse.json();
    
    if (loginData.success) {
      authToken = loginData.data.token;
      console.log('✅ Login successful');
    } else {
      console.log('❌ Login failed:', loginData.message);
      return;
    }

    // Step 2: Test get all branches
    console.log('\n2. Testing get all branches...');
    const branchesResponse = await fetch(`${BASE_URL}/branches`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const branchesData = await branchesResponse.json();
    
    if (branchesData.success) {
      console.log('✅ Branches retrieved successfully');
      console.log(`   Found ${branchesData.data.total} branches`);
      branchesData.data.branches.forEach(branch => {
        console.log(`   - ${branch.name} (${branch.city}) - Compliance: ${branch.complianceScore}%`);
      });
    } else {
      console.log('❌ Failed to get branches:', branchesData.message);
    }

    // Step 3: Test get branch snapshots (using first branch)
    if (branchesData.success && branchesData.data.branches.length > 0) {
      const firstBranchId = branchesData.data.branches[0].id;
      
      console.log('\n3. Testing get branch snapshots...');
      const snapshotsResponse = await fetch(`${BASE_URL}/snapshots/branch/${firstBranchId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      const snapshotsData = await snapshotsResponse.json();
      
      if (snapshotsData.success) {
        console.log('✅ Branch snapshots retrieved successfully');
        console.log(`   Total snapshots: ${snapshotsData.data.stats.totalSnapshots}`);
        console.log(`   Complaint snapshots: ${snapshotsData.data.stats.complaintSnapshots}`);
        console.log(`   Non-complaint snapshots: ${snapshotsData.data.stats.nonComplaintSnapshots}`);
        console.log(`   Overall compliance rate: ${snapshotsData.data.stats.overallComplianceRate}%`);
      } else {
        console.log('❌ Failed to get snapshots:', snapshotsData.message);
      }

      // Step 4: Test get snapshot details (using first snapshot)
      if (snapshotsData.success && snapshotsData.data.snapshots.length > 0) {
        const firstSnapshotId = snapshotsData.data.snapshots[0].id;
        
        console.log('\n4. Testing get snapshot details...');
        const detailsResponse = await fetch(`${BASE_URL}/snapshots/${firstSnapshotId}`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        const detailsData = await detailsResponse.json();
        
        if (detailsData.success) {
          console.log('✅ Snapshot details retrieved successfully');
          console.log(`   Snapshot type: ${detailsData.data.snapshot.type}`);
          console.log(`   Compliance rate: ${detailsData.data.snapshot.complianceRate}%`);
          console.log(`   Total reports: ${detailsData.data.snapshot.stats.totalReports}`);
          console.log(`   Reports:`);
          detailsData.data.snapshot.reports.forEach(report => {
            console.log(`     - Status: ${report.status}, Details: ${report.details}`);
          });
        } else {
          console.log('❌ Failed to get snapshot details:', detailsData.message);
        }
      }
    }

    // Step 5: Test filtering and pagination
    if (branchesData.success && branchesData.data.branches.length > 0) {
      const firstBranchId = branchesData.data.branches[0].id;
      
      console.log('\n5. Testing filtering (COMPLAINT snapshots only)...');
      const filteredResponse = await fetch(`${BASE_URL}/snapshots/branch/${firstBranchId}?type=COMPLAINT&sortBy=oldest&limit=5`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      const filteredData = await filteredResponse.json();
      
      if (filteredData.success) {
        console.log('✅ Filtered snapshots retrieved successfully');
        console.log(`   Found ${filteredData.data.snapshots.length} complaint snapshots`);
        console.log(`   Filters applied: ${JSON.stringify(filteredData.data.filters)}`);
      } else {
        console.log('❌ Failed to get filtered snapshots:', filteredData.message);
      }
    }

    console.log('\n🎉 All API tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
};

testAPI();
