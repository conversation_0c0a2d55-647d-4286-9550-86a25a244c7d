// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// datasource db {
//   provider = "sqlite"
//   url      = "file:../dev.db"
// }


// User model for authentication
model User {
  id           String   @id @default(cuid())
  name         String
  surname      String
  email        String   @unique
  password     String
  companyCode  String
  personnelCode String
  role         UserRole @default(USER)
  isActive     Boolean  @default(true)
  emailVerified Boolean @default(false)
  lastLogin    DateTime?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Indexes for better performance
  @@index([email])
  @@index([companyCode])
  @@index([personnelCode])
  @@map("users")
}

// User roles enum
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

// Snapshot type enum
enum SnapshotType {
  COMPLAINT
  NON_COMPLAINT
}

// Snapshot report status enum
enum ReportStatus {
  CHECKED
  UNCHECKED
}

// Snapshot report priority enum
enum ReportPriority {
  HIGH
  MEDIUM
  LOW
}

// Branch model for company branches
model Branch {
  id        String   @id @default(cuid())
  name      String
  city      String
  image     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  snapshots Snapshot[]
  userAccess UserAccess[]

  // Indexes for better performance
  @@index([name])
  @@index([city])
  @@map("branches")
}

// Snapshot model for branch snapshots
model Snapshot {
  id        String      @id @default(cuid())
  image     String
  type      SnapshotType
  branchId  String
  reportPDF String?
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  // Relations
  branch  Branch           @relation(fields: [branchId], references: [id], onDelete: Cascade)
  reports SnapshotReport[]

  // Indexes for better performance
  @@index([branchId])
  @@index([type])
  @@index([createdAt])
  @@map("snapshots")
}

// Snapshot report model for snapshot compliance reports
model SnapshotReport {
  id         String         @id @default(cuid())
  status     ReportStatus
  details    String?
  priority   ReportPriority @default(MEDIUM)
  snapshotId String
  createdAt  DateTime       @default(now())
  updatedAt  DateTime       @updatedAt

  // Relations
  snapshot Snapshot @relation(fields: [snapshotId], references: [id], onDelete: Cascade)

  // Indexes for better performance
  @@index([snapshotId])
  @@index([status])
  @@index([priority])
  @@map("snapshot_reports")
}

// User access model for branch-level access control
model UserAccess {
  id            String   @id @default(cuid())
  companyCode   String
  personnelCode String
  branchId      String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  branch Branch @relation(fields: [branchId], references: [id], onDelete: Cascade)

  // Indexes for better performance
  @@index([companyCode])
  @@index([personnelCode])
  @@index([branchId])
  @@index([companyCode, personnelCode])
  @@unique([companyCode, personnelCode, branchId])
  @@map("user_access")
}
