# Vista Guard Backend API Documentation

## Overview

Vista Guard Backend API provides authentication and user management services for the Vista Guard web application. The API is built with Node.js, Express.js, Prisma ORM, and MySQL database.

**Base URL:** `http://localhost:3000`  
**API Base URL:** `http://localhost:3000/api`

## Table of Contents

1. [Authentication](#authentication)
2. [API Endpoints](#api-endpoints)
   - [Health Check](#health-check)
   - [User Registration](#user-registration)
   - [User Login](#user-login)
   - [Get User Profile](#get-user-profile)
   - [User Logout](#user-logout)
   - [Get All Branches](#get-all-branches)
   - [Get Branch by ID](#get-branch-by-id)
   - [Get Branch Snapshots](#get-branch-snapshots)
   - [Get Snapshot Details](#get-snapshot-details)
   - [Get Flagged Reports](#get-flagged-reports)
3. [Error Handling](#error-handling)
4. [Status Codes](#status-codes)
5. [Database Schema](#database-schema)

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. After successful login or registration, you'll receive a JWT token that must be included in the Authorization header for protected routes.

**Header Format:**
```
Authorization: Bearer <your-jwt-token>
```

**Token Expiration:** 7 days (configurable via JWT_EXPIRES_IN environment variable)

## API Endpoints

### Health Check

Check if the API server is running and healthy.

**Endpoint:** `GET /health`  
**Authentication:** None required

#### Request
```http
GET /health HTTP/1.1
Host: localhost:3000
```

#### Response
```json
{
  "status": "OK",
  "message": "Vista Guard Backend is running",
  "timestamp": "2025-07-14T04:33:13.273Z",
  "version": "1.0.0"
}
```

---

### User Registration

Register a new user account.

**Endpoint:** `POST /api/auth/register`  
**Authentication:** None required

#### Request Payload
```json
{
  "name": "John",
  "surname": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "companyCode": "VG001",
  "personnelCode": "EMP001"
}
```

#### Field Validation
- **name**: Required, 2-50 characters
- **surname**: Required, 2-50 characters
- **email**: Required, valid email format, unique
- **password**: Required, minimum 8 characters, must contain uppercase, lowercase, number, and special character
- **companyCode**: Required, 2-20 characters
- **personnelCode**: Required, 2-20 characters

#### Success Response (201 Created)
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "cmd2lyhbi0000gpie9uvel15z",
      "name": "John",
      "surname": "Doe",
      "email": "<EMAIL>",
      "companyCode": "VG001",
      "personnelCode": "EMP001",
      "role": "USER",
      "isActive": true,
      "emailVerified": false,
      "createdAt": "2025-07-14T04:33:13.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "timestamp": "2025-07-14T04:33:13.526Z"
}
```

#### Error Response (409 Conflict - Email exists)
```json
{
  "error": true,
  "message": "User with this email already exists",
  "timestamp": "2025-07-14T04:33:13.526Z"
}
```

#### Error Response (400 Bad Request - Validation failed)
```json
{
  "error": true,
  "message": "Validation failed",
  "details": [
    {
      "field": "email",
      "message": "Please provide a valid email address",
      "value": "invalid-email"
    },
    {
      "field": "password",
      "message": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
      "value": "weak"
    }
  ],
  "timestamp": "2025-07-14T04:33:13.526Z"
}
```

---

### User Login

Authenticate a user and receive a JWT token.

**Endpoint:** `POST /api/auth/login`  
**Authentication:** None required

#### Request Payload
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

#### Field Validation
- **email**: Required, valid email format
- **password**: Required

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "cmd2lyhbi0000gpie9uvel15z",
      "name": "John",
      "surname": "Doe",
      "email": "<EMAIL>",
      "companyCode": "VG001",
      "personnelCode": "EMP001",
      "role": "USER",
      "isActive": true,
      "emailVerified": false,
      "lastLogin": "2025-07-14T04:34:23.000Z",
      "createdAt": "2025-07-14T04:33:13.000Z",
      "updatedAt": "2025-07-14T04:34:23.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "timestamp": "2025-07-14T04:34:23.726Z"
}
```

#### Error Response (401 Unauthorized - Invalid credentials)
```json
{
  "error": true,
  "message": "Invalid email or password",
  "timestamp": "2025-07-14T04:34:23.726Z"
}
```

#### Error Response (401 Unauthorized - Account deactivated)
```json
{
  "error": true,
  "message": "Account is deactivated. Please contact administrator.",
  "timestamp": "2025-07-14T04:34:23.726Z"
}
```

---

### Get User Profile

Retrieve the current authenticated user's profile information.

**Endpoint:** `GET /api/auth/profile`  
**Authentication:** Required (Bearer token)

#### Request Headers
```http
GET /api/auth/profile HTTP/1.1
Host: localhost:3000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "cmd2lyhbi0000gpie9uvel15z",
      "name": "John",
      "surname": "Doe",
      "email": "<EMAIL>",
      "companyCode": "VG001",
      "personnelCode": "EMP001",
      "role": "USER",
      "isActive": true,
      "emailVerified": false,
      "lastLogin": "2025-07-14T04:34:23.000Z",
      "createdAt": "2025-07-14T04:33:13.000Z",
      "updatedAt": "2025-07-14T04:34:23.000Z"
    }
  },
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

#### Error Response (401 Unauthorized - No token)
```json
{
  "error": true,
  "message": "Access token is required",
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

#### Error Response (401 Unauthorized - Invalid token)
```json
{
  "error": true,
  "message": "Invalid token",
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

#### Error Response (401 Unauthorized - Expired token)
```json
{
  "error": true,
  "message": "Token has expired",
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

#### Error Response (404 Not Found - User not found)
```json
{
  "error": true,
  "message": "User not found",
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

---

### User Logout

Logout the current user (client-side token removal).

**Endpoint:** `POST /api/auth/logout`  
**Authentication:** Required (Bearer token)

#### Request Headers
```http
POST /api/auth/logout HTTP/1.1
Host: localhost:3000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Logout successful. Please remove the token from client storage.",
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

**Note:** This endpoint serves as a confirmation. The actual logout is handled client-side by removing the JWT token from storage.

---
### Dashboard APIs

### Get All Branches

Retrieve all branches with compliance scores and statistics.

**Endpoint:** `GET /api/branches`
**Authentication:** Required (Bearer token)

#### Request Headers
```http
GET /api/branches HTTP/1.1
Host: localhost:3000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Branches retrieved successfully",
  "data": {
    "branches": [
      {
        "id": "clz123abc456",
        "name": "Downtown Branch",
        "city": "New York",
        "image": "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=500",
        "createdAt": "2025-07-14T04:30:00.000Z",
        "updatedAt": "2025-07-14T04:30:00.000Z",
        "complianceScore": 75,
        "stats": {
          "totalSnapshots": 3,
          "totalReports": 4,
          "checkedReports": 3,
          "uncheckedReports": 1
        }
      }
    ],
    "total": 2
  },
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

---

### Get Branch Snapshots

Retrieve snapshots for a specific branch with statistics and filtering options.

**Endpoint:** `GET /api/snapshots/branch/:branchId`
**Authentication:** Required (Bearer token)

#### Query Parameters
- `type` (optional): Filter by snapshot type (`COMPLAINT` or `NON_COMPLAINT`)
- `sortBy` (optional): Sort order (`newest` or `oldest`, default: `newest`)
- `page` (optional): Page number for pagination (default: `1`)
- `limit` (optional): Number of items per page (default: `10`)

#### Request Headers
```http
GET /api/snapshots/branch/clz123abc456?type=COMPLAINT&sortBy=newest&page=1&limit=5 HTTP/1.1
Host: localhost:3000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Branch snapshots retrieved successfully",
  "data": {
    "snapshots": [
      {
        "id": "clz456def789",
        "image": "https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=500",
        "type": "COMPLAINT",
        "createdAt": "2025-07-13T04:30:00.000Z",
        "updatedAt": "2025-07-13T04:30:00.000Z",
        "branch": {
          "id": "clz123abc456",
          "name": "Downtown Branch",
          "city": "New York"
        },
        "complianceRate": 0,
        "reportsCount": {
          "total": 1,
          "checked": 0,
          "unchecked": 1
        }
      }
    ],
    "stats": {
      "totalSnapshots": 3,
      "complaintSnapshots": 1,
      "nonComplaintSnapshots": 2,
      "overallComplianceRate": 75,
      "totalReports": 4,
      "checkedReports": 3,
      "uncheckedReports": 1
    },
    "pagination": {
      "currentPage": 1,
      "totalPages": 1,
      "totalCount": 1,
      "hasNextPage": false,
      "hasPrevPage": false,
      "limit": 5
    },
    "filters": {
      "type": "COMPLAINT",
      "sortBy": "newest"
    }
  },
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

---

### Get Snapshot Details

Retrieve detailed information about a specific snapshot including all related reports.

**Endpoint:** `GET /api/snapshots/:snapshotId`
**Authentication:** Required (Bearer token)

#### Request Headers
```http
GET /api/snapshots/clz456def789 HTTP/1.1
Host: localhost:3000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Snapshot details retrieved successfully",
  "data": {
    "snapshot": {
      "id": "clz456def789",
      "image": "https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=500",
      "type": "COMPLAINT",
      "createdAt": "2025-07-13T04:30:00.000Z",
      "updatedAt": "2025-07-13T04:30:00.000Z",
      "branch": {
        "id": "clz123abc456",
        "name": "Downtown Branch",
        "city": "New York",
        "image": "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=500"
      },
      "complianceRate": 0,
      "stats": {
        "totalReports": 1,
        "checkedReports": 0,
        "uncheckedReports": 1
      },
      "reports": [
        {
          "id": "clz789ghi012",
          "status": "UNCHECKED",
          "details": "Messy desk area needs attention",
          "createdAt": "2025-07-13T04:30:00.000Z",
          "updatedAt": "2025-07-13T04:30:00.000Z"
        }
      ]
    }
  },
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

#### Error Response (404 Not Found)
```json
{
  "error": true,
  "message": "Snapshot not found",
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

---

### Get Flagged Reports

Retrieve flagged reports (UNCHECKED status) with pagination and priority filtering.

**Endpoint:** `GET /api/snapshots/flagged`
**Authentication:** Required (Bearer token)

#### Query Parameters
- `priority` (optional): Filter by priority level (`HIGH`, `MEDIUM`, or `LOW`)
- `sortBy` (optional): Sort order (`newest`, `oldest`, or `priority`, default: `newest`)
- `page` (optional): Page number for pagination (default: `1`)
- `limit` (optional): Number of items per page (default: `10`)

#### Request Headers
```http
GET /api/snapshots/flagged?priority=HIGH&sortBy=priority&page=1&limit=10 HTTP/1.1
Host: localhost:3000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Flagged reports retrieved successfully",
  "data": {
    "reports": [
      {
        "id": "clz789ghi012",
        "status": "UNCHECKED",
        "priority": "HIGH",
        "details": "Safety equipment not properly stored",
        "createdAt": "2024-03-10T10:30:00.000Z",
        "updatedAt": "2024-03-10T10:30:00.000Z",
        "snapshot": {
          "id": "clz456def789",
          "image": "https://example.com/snapshot2.jpg",
          "type": "COMPLAINT",
          "createdAt": "2024-03-10T10:00:00.000Z",
          "branch": {
            "id": "clz123abc456",
            "name": "Downtown Branch",
            "city": "New York",
            "image": "https://example.com/branch1.jpg"
          }
        }
      }
    ],
    "stats": {
      "total": 15,
      "high": 5,
      "medium": 7,
      "low": 3
    },
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalCount": 15,
      "hasNextPage": true,
      "hasPrevPage": false,
      "limit": 10
    },
    "filters": {
      "priority": "HIGH",
      "sortBy": "priority"
    }
  },
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

#### Error Response (404 Not Found)
```json
{
  "error": true,
  "message": "No flagged reports found",
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

---

## Error Handling

All API endpoints follow a consistent error response format:

```json
{
  "error": true,
  "message": "Error description",
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

For validation errors, additional details are provided:

```json
{
  "error": true,
  "message": "Validation failed",
  "details": [
    {
      "field": "fieldName",
      "message": "Validation error message",
      "value": "submitted value"
    }
  ],
  "timestamp": "2025-07-14T04:34:23.733Z"
}
```

## Status Codes

| Status Code | Description |
|-------------|-------------|
| 200 | OK - Request successful |
| 201 | Created - Resource created successfully |
| 400 | Bad Request - Invalid request data or validation failed |
| 401 | Unauthorized - Authentication required or failed |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource already exists |
| 500 | Internal Server Error - Server error |

## Database Schema

### Users Table

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | VARCHAR(191) | PRIMARY KEY | Unique user identifier (CUID) |
| name | VARCHAR(50) | NOT NULL | User's first name |
| surname | VARCHAR(50) | NOT NULL | User's last name |
| email | VARCHAR(191) | NOT NULL, UNIQUE | User's email address |
| password | VARCHAR(255) | NOT NULL | Hashed password (bcrypt) |
| companyCode | VARCHAR(20) | NOT NULL | Company identifier |
| personnelCode | VARCHAR(20) | NOT NULL | Personnel identifier |
| role | ENUM | NOT NULL, DEFAULT 'USER' | User role (USER, ADMIN, SUPER_ADMIN) |
| isActive | BOOLEAN | NOT NULL, DEFAULT TRUE | Account status |
| emailVerified | BOOLEAN | NOT NULL, DEFAULT FALSE | Email verification status |
| lastLogin | DATETIME | NULL | Last login timestamp |
| createdAt | DATETIME | NOT NULL, DEFAULT NOW() | Account creation timestamp |
| updatedAt | DATETIME | NOT NULL, DEFAULT NOW() ON UPDATE | Last update timestamp |

### Indexes

- `idx_email` on `email` column
- `idx_companyCode` on `companyCode` column
- `idx_personnelCode` on `personnelCode` column

### Branches Table

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | VARCHAR(191) | PRIMARY KEY | Unique branch identifier (CUID) |
| name | VARCHAR(255) | NOT NULL | Branch name |
| city | VARCHAR(255) | NOT NULL | Branch city location |
| image | VARCHAR(500) | NULL | Branch image URL |
| createdAt | DATETIME | NOT NULL, DEFAULT NOW() | Branch creation timestamp |
| updatedAt | DATETIME | NOT NULL, DEFAULT NOW() ON UPDATE | Last update timestamp |

#### Indexes
- `idx_name` on `name` column
- `idx_city` on `city` column

### Snapshots Table

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | VARCHAR(191) | PRIMARY KEY | Unique snapshot identifier (CUID) |
| image | VARCHAR(500) | NOT NULL | Snapshot image URL |
| type | ENUM | NOT NULL | Snapshot type (COMPLAINT, NON_COMPLAINT) |
| branchId | VARCHAR(191) | NOT NULL, FOREIGN KEY | Reference to branches.id |
| createdAt | DATETIME | NOT NULL, DEFAULT NOW() | Snapshot creation timestamp |
| updatedAt | DATETIME | NOT NULL, DEFAULT NOW() ON UPDATE | Last update timestamp |

#### Indexes
- `idx_branchId` on `branchId` column
- `idx_type` on `type` column
- `idx_createdAt` on `createdAt` column

#### Foreign Keys
- `branchId` references `branches(id)` ON DELETE CASCADE

### Snapshot Reports Table

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | VARCHAR(191) | PRIMARY KEY | Unique report identifier (CUID) |
| status | ENUM | NOT NULL | Report status (CHECKED, UNCHECKED) |
| details | TEXT | NULL | Report details/comments |
| priority | ENUM | NOT NULL, DEFAULT MEDIUM | Report priority (HIGH, MEDIUM, LOW) |
| snapshotId | VARCHAR(191) | NOT NULL, FOREIGN KEY | Reference to snapshots.id |
| createdAt | DATETIME | NOT NULL, DEFAULT NOW() | Report creation timestamp |
| updatedAt | DATETIME | NOT NULL, DEFAULT NOW() ON UPDATE | Last update timestamp |

#### Indexes
- `idx_snapshotId` on `snapshotId` column
- `idx_status` on `status` column
- `idx_priority` on `priority` column

#### Foreign Keys
- `snapshotId` references `snapshots(id)` ON DELETE CASCADE

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| DATABASE_URL | MySQL connection string | mysql://root:@localhost:3306/vista_guard |
| JWT_SECRET | Secret key for JWT signing | (required) |
| JWT_EXPIRES_IN | JWT token expiration time | 7d |
| PORT | Server port | 3000 |
| NODE_ENV | Environment mode | development |
| FRONTEND_URL | Frontend application URL | http://localhost:5173 |
| BCRYPT_ROUNDS | Password hashing rounds | 12 |

## Sample Admin Account

A default admin account is created during database initialization:

- **Email:** <EMAIL>
- **Password:** Admin123!
- **Role:** SUPER_ADMIN
- **Company Code:** VG001
- **Personnel Code:** ADMIN001

## Getting Started

1. **Install Dependencies:**
   ```bash
   cd backend
   npm install
   ```

2. **Setup Environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Initialize Database:**
   ```bash
   node init-db.js
   ```

4. **Start Development Server:**
   ```bash
   npm run dev
   ```

5. **Seed Snapshot Data:**
   ```bash
   node create-snapshots-tables.js
   node seed-snapshots.js
   ```

6. **Test APIs:**
   ```bash
   node test-auth.js
   node test-admin-login.js
   node test-snapshots-api.js
   ```

The API server will be running at `http://localhost:3000`

---

**Last Updated:** July 14, 2025  
**API Version:** 1.0.0
